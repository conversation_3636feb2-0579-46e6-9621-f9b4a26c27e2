namespace LeadTeams.Desktop.Controls.LeadTeamsControls
{
    public class LeadTeamsTextBox : DungeonTextBox, IBindControlIdentifier
    {
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public Control MainControl => textBox;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string MainBindProperty => "Text";

        public LeadTeamsTextBox()
        {
            this.BackColor = ColorsSchema.TextBox.BackColor;
            this.ForeColor = ColorsSchema.TextBox.ForeColor;
        }
    }

    [DefaultEvent("TextChanged")]
    public class DungeonTextBox : Control
    {
        #region Variables

        protected TextBox textBox = new();
        private GraphicsPath Shape = new();
        private int _maxchars = 32767;
        private bool _ReadOnly;
        private bool _Multiline;
        private HorizontalAlignment ALNType;
        private bool isPasswordMasked = false;
        private Pen P1;
        private readonly SolidBrush B1;

        #endregion

        #region Properties

        public Color BorderColor { get; set; } = Color.FromArgb(180, 180, 180);

        public Color EdgeColor { get; set; } = Color.White;

        public HorizontalAlignment TextAlignment
        {
            get => ALNType;
            set
            {
                ALNType = value;
                Invalidate();
            }
        }
        public int MaxLength
        {
            get => _maxchars;
            set
            {
                _maxchars = value;
                textBox.MaxLength = MaxLength;
                Invalidate();
            }
        }

        private bool usePassword;
        [DefaultValue(false)]
        public bool UsePassword
        {
            get => usePassword;
            set
            {
                // Store the current text before changing the password state
                string currentText = textBox.Text;

                usePassword = value;
                UseSystemPasswordChar = value;

                // Add or remove the event handlers based on the UsePassword value
                if (value)
                {
                    textBox.MouseClick += PasswordTextBox_MouseClick;
                    MouseClick += Control_MouseClick;
                }
                else
                {
                    textBox.MouseClick -= PasswordTextBox_MouseClick;
                    MouseClick -= Control_MouseClick;
                }

                // Adjust the textbox width
                textBox.Width = value ? Width - 30 : Width - 10;

                // Restore the text
                textBox.Text = currentText;

                Invalidate();
            }
        }

        [DefaultValue(false)]
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool UseSystemPasswordChar
        {
            get => isPasswordMasked;
            set
            {
                try
                {
                    // Store current text and selection
                    string currentText = textBox.Text;
                    int selectionStart = textBox.SelectionStart;
                    int selectionLength = textBox.SelectionLength;

                    // Update password masking
                    textBox.UseSystemPasswordChar = value;
                    isPasswordMasked = value;

                    // Update password visibility state to match
                    isPasswordVisible = !value;

                    // Reset animation progress to match current state
                    animationProgress = isPasswordVisible ? 1.0f : 0.0f;

                    // Restore text and selection
                    textBox.Text = currentText;

                    // Restore selection if possible
                    if (selectionStart >= 0 && selectionStart <= textBox.Text.Length)
                    {
                        textBox.SelectionStart = selectionStart;
                        textBox.SelectionLength = selectionLength;
                    }

                    Invalidate();
                }
                catch (Exception ex)
                {
                    // Handle any exceptions
                    System.Diagnostics.Debug.WriteLine($"Error in UseSystemPasswordChar setter: {ex.Message}");
                }
            }
        }

        [DefaultValue(false)]
        public bool ReadOnly
        {
            get => _ReadOnly;
            set
            {
                _ReadOnly = value;
                if (textBox != null)
                {
                    textBox.ReadOnly = value;
                }
            }
        }

        [DefaultValue(false)]
        public bool Multiline
        {
            get => _Multiline;
            set
            {
                _Multiline = value;
                if (textBox != null)
                {
                    textBox.Multiline = value;

                    if (value)
                    {
                        textBox.Height = Height - 10;
                        textBox.ScrollBars = ScrollBars.Vertical;
                    }
                    else
                    {
                        Height = textBox.Height + 10;
                        textBox.ScrollBars = ScrollBars.None;
                    }
                }
            }
        }

        [AllowNull()]
        public new string Text { get => textBox.Text; set => textBox.Text = value; }
        #endregion

        #region EventArgs

        protected override void OnTextChanged(EventArgs e)
        {
            base.OnTextChanged(e);
            textBox.Text = Text;
            Invalidate();
        }

        private void OnBaseTextChanged(object? s, EventArgs e)
        {
            Text = textBox.Text;
        }

        protected override void OnForeColorChanged(EventArgs e)
        {
            base.OnForeColorChanged(e);
            textBox.ForeColor = ForeColor;
            Invalidate();
        }

        protected override void OnFontChanged(EventArgs e)
        {
            base.OnFontChanged(e);
            textBox.Font = Font;
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            base.OnPaintBackground(e);
        }

        private void _OnKeyDown(object? Obj, KeyEventArgs e)
        {
            if (e.Control && e.KeyCode == Keys.A)
            {
                textBox.SelectAll();
                e.SuppressKeyPress = true;
            }
            if (e.Control && e.KeyCode == Keys.C)
            {
                textBox.Copy();
                e.SuppressKeyPress = true;
            }
        }

        private void _Enter(object? Obj, EventArgs e)
        {
            P1 = new(Color.FromArgb(205, 87, 40));
            Refresh();
        }

        private void _Leave(object? Obj, EventArgs e)
        {
            P1 = new(Color.FromArgb(180, 180, 180));
            Refresh();
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            if (_Multiline)
            {
                textBox.Height = Height - 10;
            }
            else
            {
                Height = textBox.Height + 10;
            }

            Shape = new();
            GraphicsPath _with1 = Shape;
            _with1.AddArc(0, 0, 10, 10, 180, 90);
            _with1.AddArc(Width - 11, 0, 10, 10, -90, 90);
            _with1.AddArc(Width - 11, Height - 11, 10, 10, 0, 90);
            _with1.AddArc(0, Height - 11, 10, 10, 90, 90);
            _with1.CloseAllFigures();
        }

        protected override void OnGotFocus(EventArgs e)
        {
            base.OnGotFocus(e);
            textBox.Focus();
        }

        #endregion

        public void AddTextBox()
        {
            TextBox _TB = textBox;
            // Make the textbox slightly smaller to leave room for the eye icon
            _TB.Size = new(UsePassword ? Width - 30 : Width - 10, 33);
            _TB.Location = new(7, 4);
            _TB.Text = string.Empty;
            _TB.BorderStyle = BorderStyle.None;
            _TB.TextAlign = HorizontalAlignment.Left;
            _TB.Font = Font;
            _TB.UseSystemPasswordChar = UseSystemPasswordChar;
            _TB.Multiline = false;
            textBox.KeyDown += _OnKeyDown;
            textBox.Enter += _Enter;
            textBox.Leave += _Leave;
            textBox.TextChanged += OnBaseTextChanged;
        }

        public DungeonTextBox()
        {
            SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            SetStyle(ControlStyles.UserPaint, true);

            AddTextBox();
            Controls.Add(textBox);

            P1 = new(BorderColor); // P1 = Border color
            B1 = new(EdgeColor); // B1 = Rect Background color
            BackColor = Color.Transparent;
            ForeColor = Color.DimGray;

            Text = string.Empty;
            Font = new("Tahoma", 11);
            Size = new(135, 33);
            DoubleBuffered = true;

            // Setup animation timer
            animationTimer.Tick += AnimationTimer_Tick;

            // Initialize animation progress based on password state
            animationProgress = isPasswordVisible ? 1.0f : 0.0f;

            // Handle disposal of resources
            Disposed += (s, e) =>
            {
                try
                {
                    animationTimer.Stop();
                    animationTimer.Tick -= AnimationTimer_Tick;
                    P1?.Dispose();
                    B1?.Dispose();
                }
                catch { /* Ignore disposal errors */ }
            };
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Dispose managed resources
                try
                {
                    animationTimer.Stop();
                    animationTimer.Tick -= AnimationTimer_Tick;
                    P1?.Dispose();
                    B1?.Dispose();
                }
                catch { /* Ignore disposal errors */ }
            }

            base.Dispose(disposing);
        }

        private void AnimationTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                // Update animation progress with more gradual steps
                if (isPasswordVisible)
                {
                    animationProgress += 0.15f; // Slower animation for smoother transition
                    if (animationProgress >= 1.0f)
                    {
                        animationProgress = 1.0f;
                        animationTimer.Stop();
                    }
                }
                else
                {
                    animationProgress -= 0.15f; // Slower animation for smoother transition
                    if (animationProgress <= 0.0f)
                    {
                        animationProgress = 0.0f;
                        animationTimer.Stop();
                    }
                }

                // Redraw the control
                if (!IsDisposed && Visible)
                {
                    Invalidate();
                }
            }
            catch (Exception ex)
            {
                // Handle any exceptions that might occur
                System.Diagnostics.Debug.WriteLine($"Error in AnimationTimer_Tick: {ex.Message}");

                // Make sure timer is stopped if an exception occurs
                try { animationTimer.Stop(); } catch { /* Ignore */ }
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            Bitmap B = new(Width, Height);
            Graphics G = Graphics.FromImage(B);

            // Set high quality rendering settings
            G.SmoothingMode = SmoothingMode.HighQuality;
            G.InterpolationMode = InterpolationMode.HighQualityBicubic;
            G.PixelOffsetMode = PixelOffsetMode.HighQuality;
            G.CompositingQuality = CompositingQuality.HighQuality;

            TextBox _TB = textBox;
            // Adjust width based on whether password mode is enabled
            _TB.Width = UsePassword ? Width - 30 : Width - 10;
            _TB.TextAlign = TextAlignment;
            _TB.UseSystemPasswordChar = UseSystemPasswordChar;

            G.Clear(BackColor);
            G.FillPath(B1, Shape); // Draw background
            G.DrawPath(P1, Shape); // Draw border

            e.Graphics.DrawImage((Image)B.Clone(), 0, 0);

            // Apply high quality rendering settings to e.Graphics as well
            if (UsePassword)
            {
                e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
                // Define icon position and size
                int iconX = Width - 20; // Position near the right side
                int iconY = (Height / 2) - 8;
                int iconSize = 18;

                // Create colors based on state
                Color iconColor = isIconHovered ? Color.FromArgb(0, 120, 215) : Color.FromArgb(100, 100, 100);
                Color fillColor = isIconHovered ? Color.FromArgb(230, 240, 250) : Color.FromArgb(240, 240, 240);

                // Draw icon background with high precision
                using var bgBrush = new SolidBrush(fillColor);

                // Calculate precise background coordinates for sharper rendering
                float bgX = iconX - 1;
                float bgY = iconY - 1;
                float bgSize = iconSize + 2;

                // Ensure background is on pixel boundaries for sharpness
                float adjustedBgX = (float)Math.Round((double)bgX) + 0.5f;
                float adjustedBgY = (float)Math.Round((double)bgY) + 0.5f;
                float adjustedBgSize = (float)Math.Round((double)bgSize) - 1f;

                // Draw with high precision
                e.Graphics.FillEllipse(bgBrush, adjustedBgX, adjustedBgY, adjustedBgSize, adjustedBgSize);

                // Create pens for drawing with high quality settings
                using var pen = new Pen(iconColor, isIconHovered ? 2 : 1.5f)
                {
                    StartCap = LineCap.Round,
                    EndCap = LineCap.Round,
                    Alignment = PenAlignment.Center
                };

                // Calculate animation values
                float openProgress = isPasswordVisible ? animationProgress : 1 - animationProgress;
                float closedProgress = 1 - openProgress;

                // Eye height based on animation (from flat to circular)
                float eyeHeight = iconSize / 2 + iconSize / 2 * openProgress;

                // Draw eye outline with animated shape - use precise coordinates
                float eyeY = iconY + (iconSize - eyeHeight) / 2;

                // For sharper circles, ensure coordinates are on pixel boundaries
                float adjustedX = (float)Math.Round((double)iconX) + 0.5f;
                float adjustedY = (float)Math.Round((double)eyeY) + 0.5f;
                float adjustedWidth = (float)Math.Round((double)iconSize) - 1f;
                float adjustedHeight = (float)Math.Round((double)eyeHeight) - 1f;

                // Draw with high precision
                e.Graphics.DrawEllipse(pen, adjustedX, adjustedY, adjustedWidth, adjustedHeight);

                // Draw pupil with animated opacity
                if (openProgress > 0.1f)
                {
                    using var pupilBrush = new SolidBrush(Color.FromArgb(
                        (int)(openProgress * 255),
                        iconColor.R,
                        iconColor.G,
                        iconColor.B));

                    // Calculate precise pupil size and position for sharper rendering
                    float pupilSize = 3 + openProgress;
                    float pupilX = adjustedX + (adjustedWidth / 2) - (pupilSize / 2);
                    float pupilY = adjustedY + (adjustedHeight / 2) - (pupilSize / 2);

                    // Ensure pupil is on pixel boundaries for sharpness
                    float adjustedPupilX = (float)Math.Round((double)pupilX) + 0.5f;
                    float adjustedPupilY = (float)Math.Round((double)pupilY) + 0.5f;
                    float adjustedPupilSize = (float)Math.Round((double)pupilSize) - 1f;

                    // Draw with high precision
                    e.Graphics.FillEllipse(
                        pupilBrush,
                        adjustedPupilX,
                        adjustedPupilY,
                        adjustedPupilSize,
                        adjustedPupilSize);
                }

                // Draw diagonal line with animated opacity
                if (closedProgress > 0.1f)
                {
                    using var closedPen = new Pen(Color.FromArgb(
                        (int)(closedProgress * 255),
                        iconColor.R,
                        iconColor.G,
                        iconColor.B),
                        pen.Width)
                    {
                        StartCap = pen.StartCap,
                        EndCap = pen.EndCap
                    };

                    // Calculate precise line coordinates for sharper rendering
                    float lineStartX = adjustedX + 2;
                    float lineStartY = adjustedY + 2;
                    float lineEndX = adjustedX + adjustedWidth - 2;
                    float lineEndY = adjustedY + adjustedHeight - 2;

                    // Ensure line endpoints are on pixel boundaries for sharpness
                    float adjustedLineStartX = (float)Math.Round((double)lineStartX) + 0.5f;
                    float adjustedLineStartY = (float)Math.Round((double)lineStartY) + 0.5f;
                    float adjustedLineEndX = (float)Math.Round((double)lineEndX) + 0.5f;
                    float adjustedLineEndY = (float)Math.Round((double)lineEndY) + 0.5f;

                    // Draw with high precision
                    e.Graphics.DrawLine(
                        closedPen,
                        adjustedLineStartX,
                        adjustedLineStartY,
                        adjustedLineEndX,
                        adjustedLineEndY);
                }
            }

            G.Dispose();
            B.Dispose();
        }

        private bool isPasswordVisible = false;
        private bool isIconHovered = false;
        private float animationProgress = 0f;
        private readonly System.Windows.Forms.Timer animationTimer = new() { Interval = 20 };
        private bool isProcessingClick = false; // Flag to prevent multiple rapid clicks

        private void PasswordTextBox_MouseClick(object? sender, MouseEventArgs e)
        {
            TogglePasswordVisibility(e.X, e.Y);
        }

        private void Control_MouseClick(object? sender, MouseEventArgs e)
        {
            TogglePasswordVisibility(e.X, e.Y);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);

            if (UsePassword)
            {
                // Define the eye icon area
                int iconX = Width - 20;
                int iconY = (Height / 2) - 8;
                int iconSize = 18;

                // Check if mouse is over the icon area
                bool wasHovered = isIconHovered;
                isIconHovered = e.X >= iconX - 5 && e.X <= iconX + iconSize + 5 && e.Y >= iconY - 5 && e.Y <= iconY + iconSize + 5;

                // Only redraw if hover state changed
                if (wasHovered != isIconHovered)
                {
                    Invalidate();
                    Cursor = isIconHovered ? Cursors.Hand : Cursors.Default;
                }
            }
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            base.OnMouseLeave(e);

            if (isIconHovered)
            {
                isIconHovered = false;
                Invalidate();
                Cursor = Cursors.Default;
            }
        }

        private void TogglePasswordVisibility(int x, int y)
        {
            // Prevent multiple rapid clicks that could cause exceptions
            if (isProcessingClick)
                return;

            try
            {
                // Set processing flag to prevent re-entry
                isProcessingClick = true;

                // Define the eye icon area
                int iconX = Width - 20;
                int iconY = (Height / 2) - 8;
                int iconSize = 18;

                // Check if click is within the eye icon area or close to it
                if (x >= iconX - 5 && x <= iconX + iconSize + 5 && y >= iconY - 5 && y <= iconY + iconSize + 5)
                {
                    // Store the current text and selection state
                    string currentText = textBox.Text;
                    int selectionStart = textBox.SelectionStart;
                    int selectionLength = textBox.SelectionLength;

                    // Make sure any existing animation is stopped
                    animationTimer.Stop();

                    // Toggle password visibility
                    isPasswordVisible = !isPasswordVisible;

                    // Update the password char setting without triggering the property
                    textBox.UseSystemPasswordChar = !isPasswordVisible;
                    isPasswordMasked = !isPasswordVisible;

                    // Restore the text and selection
                    textBox.Text = currentText;

                    // Restore selection if possible
                    if (selectionStart >= 0 && selectionStart <= textBox.Text.Length)
                    {
                        textBox.SelectionStart = selectionStart;
                        textBox.SelectionLength = selectionLength;
                    }

                    // Start animation without blocking the UI thread
                    animationTimer.Interval = 30; // Slightly slower initial interval for stability
                    animationTimer.Start();

                    Invalidate(); // Redraw to reflect the change
                }
            }
            catch (Exception ex)
            {
                // Handle any exceptions that might occur
                System.Diagnostics.Debug.WriteLine($"Error in TogglePasswordVisibility: {ex.Message}");
            }
            finally
            {
                // Always reset the processing flag
                isProcessingClick = false;
            }
        }
    }
}
