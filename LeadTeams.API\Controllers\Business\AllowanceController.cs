﻿namespace LeadTeams.API.Controllers.Business
{
    public class AllowanceController : BaseBusinessController<AllowanceModel, AllowanceModel, CreateAllowanceViewModel, UpdateAllowanceViewModel>
    {
        private readonly IAllowanceService _allowanceService;

        public AllowanceController(IAllowanceService allowanceService) : base(allowanceService)
        {
            _allowanceService = allowanceService;
        }
    }
}
