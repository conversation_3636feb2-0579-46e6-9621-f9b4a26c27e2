﻿namespace LeadTeams.Desktop.Services.BaseView
{
    public partial class BaseListGenericView<TEntity, TEntityView> : BaseListView
        where TEntity : BaseIdentityModel
        where TEntityView : BaseIdentityModel, new()
    {
        private readonly IBaseService<TEntity, TEntityView> _baseService;
        protected TEntityView SearchValueModel = new TEntityView();

        //For design time support
        public BaseListGenericView() : base()
        {
            InitializeComponent();
        }

        public BaseListGenericView(IBaseService<TEntity, TEntityView> baseService, string viewScreenName) : base(viewScreenName)
        {
            InitializeComponent();

            _baseService = baseService;

            LoadData(PageNumber, PageSize);
        }

        public BaseListGenericView(IBaseService<TEntity, TEntityView> baseService, string viewScreenName, PaginationDataGridView paginationList) : base(viewScreenName, paginationList)
        {
            InitializeComponent();

            _baseService = baseService;

            LoadData(PageNumber, PageSize);
        }

        public void SearchAction()
        {
            if (DataSourceHandler == null)
            {
                MessageBox.Show("Need To Set The Data Source Handler So Can Start Serching");
                return;
            }

            SearchBoxGenericView<TEntityView> searchBox = new SearchBoxGenericView<TEntityView>(SearchValueModel, listControlValue, DataSourceHandler);
            if (searchBox.ShowDialog() == DialogResult.OK)
            {
                IsSearched = true;
                listControlValue = searchBox.listControlValue;
                SearchValueModel = searchBox.GetValuesAsModel<TEntityView>();
                LoadData(PageNumber, PageSize);
            }
        }

        public override void Clear()
        {
            base.Clear();
            SearchValueModel = new TEntityView();
        }

        public override void FilterFunction()
        {
            base.FilterFunction();
            SearchAction();
        }

        public override PaginationList LoadAllData(int pageNumber, int pageSize)
        {
            try
            {
                ServiceResult<PaginationList<TEntityView>>? result = null;
                if (IsSearched)
                    result = _baseService.GetAllViewBy(SearchValueModel, pageNumber, pageSize);
                else
                    result = _baseService.GetAllView(pageNumber, pageSize);

                if (result == null || result.Data == null)
                {
                    MessageBox.Show("No Data Found");
                    return new PaginationList(0, 0, 0);
                }

                return result.Data;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return new PaginationList(0, 0, 0);
            }
        }

        public override void DeleteFunction()
        {
            try
            {
                base.DeleteFunction();

                var result = MessageBox.Show("Delete Current Selected Row ?", "Delete !", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation);
                if (result == DialogResult.Yes)
                {
                    if (GetDataGridView.CurrentRow.DataBoundItem != null)
                    {
                        TEntityView? model = GetDataGridView.CurrentRow.DataBoundItem as TEntityView;
                        if (model != null)
                        {
                            _baseService.Remove(model.Id);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}
