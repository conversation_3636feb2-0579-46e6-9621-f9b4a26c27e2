﻿namespace GMCadiomCore.Services.Server.Helper
{
    public static class Search
    {
        public static Expression<Func<T, bool>> GenerateSearchExpression<T>(T searchValues) where T : class
        {
            ParameterExpression parameterExpression = Expression.Parameter(typeof(T), "x");
            Expression? combinedExpression = null;

            // Get all properties that have the Searchable attribute
            var properties = TypeDescriptor.GetProperties(typeof(T))
                .Cast<PropertyDescriptor>()
                .Where(prop => prop.Attributes[typeof(SearchableAttribute)] != null)
                .ToArray();

            foreach (var property in properties)
            {
                var value = property.GetValue(searchValues);
                if (value != null && !IsDefault(value))
                {
                    MemberExpression memberExpression = Expression.Property(parameterExpression, property.Name);

                    Expression? comparison = null;
                    if (property.PropertyType == typeof(int) || property.PropertyType == typeof(int?))
                    {
                        // Handle int and nullable int
                        int cleanValue = ValidateValue.ValidateInt(value);
                        if (cleanValue > 0)
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(int));
                            comparison = property.PropertyType == typeof(int?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.Equal(memberExpression, constantExpression);
                        }
                    }
                    else if (property.PropertyType == typeof(string))
                    {
                        // Handle string
                        string cleanValue = ValidateValue.ValidateString(value);
                        if (!string.IsNullOrEmpty(cleanValue))
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(string));
                            var containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) });
                            if (containsMethod != null)
                                comparison = Expression.Call(memberExpression, containsMethod, constantExpression);
                        }
                    }
                    // Handle other data types similarly (e.g., decimal, bool, etc.)
                    else if (property.PropertyType == typeof(decimal) || property.PropertyType == typeof(decimal?))
                    {
                        decimal cleanValue = ValidateValue.ValidateDecimal(value);
                        if (cleanValue > 0)
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(decimal));
                            comparison = property.PropertyType == typeof(decimal?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.Equal(memberExpression, constantExpression);
                        }
                    }
                    else if (property.PropertyType == typeof(bool) || property.PropertyType == typeof(bool?))
                    {
                        bool cleanValue = ValidateValue.ValidateBool(value);
                        var constantExpression = Expression.Constant(cleanValue, typeof(bool));
                        comparison = property.PropertyType == typeof(bool?)
                            ? Expression.AndAlso(
                                Expression.Property(memberExpression, "HasValue"),
                                Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                              )
                            : Expression.Equal(memberExpression, constantExpression);
                    }

                    // Combine the comparison expression with the previous ones using AndAlso
                    if (comparison != null)
                    {
                        combinedExpression = combinedExpression == null
                            ? comparison
                            : Expression.AndAlso(combinedExpression, comparison);
                    }
                }
            }

            // Return the combined expression as a lambda expression
            return combinedExpression != null
                ? Expression.Lambda<Func<T, bool>>(combinedExpression, parameterExpression)
                : x => true; // Default expression if no filters are applied
        }

        private static bool IsDefault(object value)
        {
            if (value == null)
                return true;

            var type = value.GetType();
            if (type.IsValueType)
            {
                var obj = Activator.CreateInstance(type);
                if (obj != null)
                {
                    return obj.Equals(value);
                }
            }

            return false;
        }
    }
}
