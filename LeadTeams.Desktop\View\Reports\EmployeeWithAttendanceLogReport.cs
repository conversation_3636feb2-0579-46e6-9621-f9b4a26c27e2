﻿namespace LeadTeams.Desktop.View.Reports
{
    public partial class EmployeeWithAttendanceLogReport : GenericViewTable.GenericEmployeeWithAttendanceLogReportView
    {
        private readonly ISession _session;
        private readonly IAttendanceService _attendanceService;

        public EmployeeWithAttendanceLogReport(ISession session, IAttendanceService attendanceService)
        {
            InitializeComponent();

            _session = session;
            _attendanceService = attendanceService;
            DataBaseWatcherEntityName = nameof(AttendanceLogModel);

            AssociateAndRaiseEvents();

            RefreshListFunction();
        }

        private void AssociateAndRaiseEvents()
        {
            tpdPeriodValue.ValueChanged += (s, e) => RefreshListFunction();
            viewList.GetdgvTotalsSummary.DGV.DataBindingComplete += (s, e) =>
            {
                var lst = MainList.Items.ToList();

                if (!viewList.GetdgvTotalsSummary.DGV.Columns.Contains(RunningTotal))
                    viewList.GetdgvTotalsSummary.DGV.Columns.Add(RunningTotal);

                if (e.ListChangedType != ListChangedType.Reset) return;

                long total = 0;
                for (int i = 0; i < lst.Count; ++i)
                {
                    AttendanceLogReport a = lst[i];
                    DataGridViewRow r = viewList.GetdgvTotalsSummary.DGV.Rows[i];
                    total += a.TotalSeconds;
                    var timeSpan = TimeSpan.FromSeconds(total);
                    string value = $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
                    r.Cells[nameof(RunningTotal)].Value = value;
                }
            };
        }


        public override void MainData()
        {
            if (tpdPeriodValue.Value == Ulid.Empty)
                return;
            string groupBy = TargetTimeDurationEnumeration.FromValue(tpdPeriodValue.Value).Name;
            List<AttendanceLogReport> result = _attendanceService.GetEmployeeAttendanceLog(groupBy, _session.Organization.Id);
            MainList = PaginationList<AttendanceLogReport>.Create(result, PageNumber, PageSize);
            base.MainData();
        }

        //Methods
        protected override void AddViewListControl(BindingSource list)
        {
            tableLayoutPanel1.SetColumnSpan(viewList, 2);
            tableLayoutPanel1.Controls.Add(viewList, 0, 1);

            //Select Column Name To Sum
            viewList.GetdgvTotalsSummary.SummaryColumns = new string[] { nameof(AttendanceLogReport.TotalSeconds) };

        }
    }
}
