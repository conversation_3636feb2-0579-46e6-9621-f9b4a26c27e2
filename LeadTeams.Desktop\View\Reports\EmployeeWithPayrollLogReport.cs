﻿namespace LeadTeams.Desktop.View.Reports
{
    public partial class EmployeeWithPayrollLogReport : GenericViewTable.GenericEmployeePayrollLogReportView
    {
        private readonly ISession _session;
        private readonly IPayrollService _payrollService;

        public EmployeeWithPayrollLogReport(ISession session, IPayrollService payrollService)
        {
            InitializeComponent();

            _session = session;
            _payrollService = payrollService;
            DataBaseWatcherEntityName = nameof(AttendanceLogModel);

            AssociateAndRaiseEvents();

            RefreshListFunction();
        }

        private void AssociateAndRaiseEvents()
        {
            viewList.GetdgvTotalsSummary.DGV.DataBindingComplete += (s, e) =>
            {
                var lst = MainList.Items.ToList();

                if (!viewList.GetdgvTotalsSummary.DGV.Columns.Contains(RunningTotal))
                    viewList.GetdgvTotalsSummary.DGV.Columns.Add(RunningTotal);

                if (e.ListChangedType != ListChangedType.Reset) return;

                decimal total = 0;
                for (int i = 0; i < lst.Count; ++i)
                {
                    PayrollReport a = lst[i];
                    DataGridViewRow r = viewList.GetdgvTotalsSummary.DGV.Rows[i];
                    total += a.NetAmount;
                    r.Cells[nameof(RunningTotal)].Value = total;
                }
            };
        }

        public override void MainData()
        {
            var result = _payrollService.GetPayrollReports(_session.Organization.Id);
            MainList = PaginationList<PayrollReport>.Create(result, PageNumber, PageSize);
            base.MainData();
        }

        //Methods
        protected override void AddViewListControl(BindingSource List)
        {
            tableLayoutPanel1.Controls.Add(viewList, 0, 0);

            //Select Column Name To Sum
            viewList.GetdgvTotalsSummary.SummaryColumns = new string[] { nameof(PayrollReport.NetAmount), nameof(PayrollReport.AttendanceLogDuration) };
        }
    }
}
