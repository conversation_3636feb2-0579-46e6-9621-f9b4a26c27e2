﻿global using GMCadiomCore.Authentications.PermissionsAndSessions;
global using GMCadiomCore.Desktop.CustomControls.dgv;
global using GMCadiomCore.Desktop.Extensions;
global using GMCadiomCore.Desktop.Loading;
global using GMCadiomCore.Desktop.Shared.Helper;
global using GMCadiomCore.Desktop.View;
global using GMCadiomCore.Models.BaseModels;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.ModelValidation.CustomAttribute;
global using GMCadiomCore.Models.ResultPattern;
global using GMCadiomCore.Models.ViewModel;
global using GMCadiomCore.Repositories.Factory;
global using GMCadiomCore.Repositories.IFactory;
global using GMCadiomCore.Services.Core;
global using GMCadiomCore.Shared.Extensions;
global using GMCadiomCore.Shared.Helper;
global using LeadTeams.Desktop.Controls.CustomDataGridView;
global using LeadTeams.Desktop.Controls.LeadTeamsControls;
global using LeadTeams.Desktop.Controls.Utilities;
global using LeadTeams.Models.ViewModel.BaseModels;
global using LeadTeams.PermissionAndSession.Authentication;
global using LeadTeams.Services.Core.BaseService;
global using System.ComponentModel;
