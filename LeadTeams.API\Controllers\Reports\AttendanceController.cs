﻿namespace LeadTeams.API.Controllers.Reports
{
    [Authorize]
    [Route("api/reports/[controller]")]
    [ApiController]
    public class AttendanceController : ControllerBase
    {
        private readonly IAttendanceService _attendanceService;

        public AttendanceController(IAttendanceService attendanceService)
        {
            _attendanceService = attendanceService;
        }

        [HttpGet("GetEmployeeAttendanceLog")]
        public IActionResult GetEmployeeAttendanceLog(string groupBy, Ulid organizationId, Ulid? employeeId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var result = _attendanceService.GetEmployeeAttendanceLog(groupBy, organizationId, employeeId, startDate, endDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
