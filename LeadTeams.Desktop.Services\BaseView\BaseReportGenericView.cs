﻿namespace LeadTeams.Desktop.Services.BaseView
{
    public partial class BaseReportGenericView<TEntityView> : BaseReportView
        where TEntityView : new()
    {
        protected PaginationList<TEntityView> MainList;
        protected dgvViewList<TEntityView> viewList;
        private TEntityView SearchValueModel = new TEntityView();

        public BaseReportGenericView()
        {
            InitializeComponent();

            MainBindingSource = new BindingSource();

            CreateViewList(new dgvViewList<TEntityView>(MainBindingSource));
            SetBindingSource();
            AssociateAndRaiseEvents();
        }

        private void AssociateAndRaiseEvents()
        {
            this.Shown += (s, e) => AddViewListControl(MainBindingSource);
        }

        public virtual void MainData()
        {
        }

        protected virtual void CreateViewList(dgvViewList<TEntityView> dgvViewList)
        {
            viewList = dgvViewList;
        }

        private void SetBindingSource()
        {
            viewList.Dock = DockStyle.Fill;
            ApplyPaginationEvents(viewList);
        }

        private void ApplyPaginationEvents(dgvViewList<TEntityView> dgvViewList)
        {
            dgvViewList.RefreshData += PaginationList_Refresh;
            dgvViewList.Previous += PaginationList_Previous;
            dgvViewList.Next += PaginationList_Next;
            dgvViewList.First += PaginationList_First;
            dgvViewList.Last += PaginationList_Last;
        }

        protected virtual void AddViewListControl(BindingSource list)
        {
            throw new NotImplementedException();
        }

        private void LoadAllActionList()
        {
            LoadingHelper.ShowLoading("Loading ... Please wait ........", o =>
            {
                MainData();
                MainBindingSource.DataSource = MainList;
                if (viewList != null && MainList != null)
                    viewList.Header = $"Page {PageNumber} / {(MainList.TotalPages == 0 ? 1 : MainList.TotalPages)}";
            });
        }

        private void SearchAction()
        {
            if (DataSourceHandler == null)
            {
                MessageBox.Show("Need To Set The Data Source Handler So Can Start Serching");
                return;
            }

            SearchBoxGenericView<TEntityView> searchBox = new SearchBoxGenericView<TEntityView>(SearchValueModel, listControlValue, DataSourceHandler);
            if (searchBox.ShowDialog() == DialogResult.OK)
            {
                listControlValue = searchBox.listControlValue;
                SearchValueModel = searchBox.GetValuesAsModel<TEntityView>();
                LoadAllActionList();
            }
        }

        public override void Clear()
        {
            base.Clear();
            SearchValueModel = new TEntityView();
        }

        #region Buttons
        public override void RefreshListFunction()
        {
            LoadingHelper.ShowLoading("Loading ... Please wait ........", o =>
            {
                base.RefreshListFunction();
                LoadAllActionList();
            });
        }

        public override void ExportFunction()
        {
            base.ExportFunction();
            Excel.ExportFromDGV(viewList.GetdgvTotalsSummary.DGV);
        }

        public override void FilterFunction()
        {
            base.FilterFunction();
            SearchAction();
        }
        #endregion

        protected void SetColumnDisplayIndex(string columnName, int displayIndex)
        {
            if (viewList.GetdgvTotalsSummary.DGV.Columns.Contains(columnName))
                viewList.GetdgvTotalsSummary.DGV.Columns[columnName].DisplayIndex = displayIndex;
        }

        protected void SetColumnVisable(string columnName, bool visible)
        {
            if (viewList.GetdgvTotalsSummary.DGV.Columns.Contains(columnName))
                viewList.GetdgvTotalsSummary.DGV.Columns[columnName].Visible = visible;
        }

        #region Pagination
        protected int PageNumber = 1;
        protected int PageSize => viewList.PageSize;

        private void PaginationList_Refresh(object? sender, EventArgs e)
        {
            LoadAllActionList();
        }

        private void PaginationList_Previous(object? sender, EventArgs e)
        {
            if (MainList != null)
                if (MainList.HasPreviousPage)
                {
                    --PageNumber;
                    LoadAllActionList();
                }
        }

        private void PaginationList_Next(object? sender, EventArgs e)
        {
            if (MainList != null)
                if (MainList.HasNextPage)
                {
                    ++PageNumber;
                    LoadAllActionList();
                }
        }

        private void PaginationList_First(object? sender, EventArgs e)
        {
            PageNumber = 1;
            LoadAllActionList();
        }

        private void PaginationList_Last(object? sender, EventArgs e)
        {
            if (MainList != null)
                PageNumber = MainList.TotalPages;
            LoadAllActionList();
        }
        #endregion
    }
}
