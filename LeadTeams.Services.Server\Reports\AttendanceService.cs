﻿namespace LeadTeams.Services.Server.Reports
{
    public class AttendanceService : IAttendanceService
    {
        IUnitOfWork _unitOfWork;

        public AttendanceService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public List<AttendanceLogReport> GetEmployeeAttendanceLog(string groupBy, Ulid organizationId, Ulid? employeeId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            if (string.IsNullOrWhiteSpace(groupBy))
                throw new ArgumentNullException(nameof(groupBy));

            switch (groupBy.ToLower())
            {
                case "daily":
                    return _unitOfWork.AttendanceLog.GetAttendanceLogByDaily(organizationId, employeeId, startDate, endDate);
                case "weekly":
                    return _unitOfWork.AttendanceLog.GetAttendanceLogByWeekly(organizationId, employeeId, startDate, endDate);
                case "monthly":
                    return _unitOfWork.AttendanceLog.GetAttendanceLogByMonthly(organizationId, employeeId, startDate, endDate);
                default:
                    return new List<AttendanceLogReport>();
            }
        }
    }

    public static class AttendanceServiceTest
    {
        private static EmployeeModel GetEmployee(string id)
        {
            EmployeeModel employee = new EmployeeModel()
            {
                Id = Ulid.Parse(id),
                EmployeeName = "Mohamed Gamal",
                EmployeeGender = GenderEnumeration.Male.Name,
                EmployeeCustomID = "EMP_007",
                EmployeeAllowances = new List<EmployeeAllowanceModel>()
                {
                    new EmployeeAllowanceModel()
                    {
                        Allowance = new AllowanceModel()
                        {
                            EmployeeAllowanceName =  "Internet",
                            EmployeeAllowanceDuration = EmployeeAllowanceDurationEnumeration.Monthly.Name,
                        },
                        EmployeeAllowanceAmount = 250,
                    }
                },
            };

            return employee;
        }

        private static ShiftDto GetShift()
        {
            ShiftDto shift = new ShiftDto()
            {
                ShiftName = "main shift",
                IsFixedPattern = false,
                ShiftDynamicPatterns = new List<ShiftDynamicPatternDto>()
                {
                    new ShiftDynamicPatternDto() { DayOfWeek = (int)Enums.DayOfWeek.Saturday, TargetTime = new TimeSpan(8,0,0),},
                    new ShiftDynamicPatternDto() { DayOfWeek = (int)Enums.DayOfWeek.Sunday, TargetTime = new TimeSpan(8,0,0),},
                    new ShiftDynamicPatternDto() { DayOfWeek = (int)Enums.DayOfWeek.Monday, TargetTime = new TimeSpan(8,0,0),},
                    new ShiftDynamicPatternDto() { DayOfWeek = (int)Enums.DayOfWeek.Tuesday, TargetTime = new TimeSpan(8,0,0),},
                    new ShiftDynamicPatternDto() { DayOfWeek = (int)Enums.DayOfWeek.Wednesday, TargetTime = new TimeSpan(8,0,0),},
                }.ToList(),
            };

            return shift;
        }

        private static string shiftJson = Shared.Helper.JsonUtilities.SaveToJsonString(GetShift());

        public static List<AttendanceLogReport> GetAttendanceLogReports()
        {
            List<AttendanceLogReport> attendanceLogReports = new List<AttendanceLogReport>()
            {
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,1,19,50,0), EndDateTime =  new DateTime(2024,7,1,22,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,2,19,30,0), EndDateTime =  new DateTime(2024,7,2,22,10,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,5,16,0,0), EndDateTime =  new DateTime(2024,7,5,23,20,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,6,13,0,0), EndDateTime =  new DateTime(2024,7,6,18,47,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,7,19,0,0), EndDateTime =  new DateTime(2024,7,7,20,24,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,8,12,30,0), EndDateTime =  new DateTime(2024,7,8,15,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,10,20,0,0), EndDateTime =  new DateTime(2024,7,10,23,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,11,8,30,0), EndDateTime =  new DateTime(2024,7,11,13,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,12,16,50,0), EndDateTime =  new DateTime(2024,7,12,19,5,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,13,16,20,0), EndDateTime =  new DateTime(2024,7,13,21,30,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,14,18,0,0), EndDateTime =  new DateTime(2024,7,14,22,40,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,15,17,30,0), EndDateTime =  new DateTime(2024,7,16,0,5,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,16,9,0,0), EndDateTime =  new DateTime(2024,7,16,11,30,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,17,8,10,0), EndDateTime =  new DateTime(2024,7,17,13,30,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,18,8,10,0), EndDateTime =  new DateTime(2024,7,18,16,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,19,20,45,0), EndDateTime =  new DateTime(2024,7,19,23,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,20,15,35,0), EndDateTime =  new DateTime(2024,7,20,21,55,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,21,8,30,0), EndDateTime =  new DateTime(2024,7,21,13,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,22,9,30,0), EndDateTime =  new DateTime(2024,7,22,16,10,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,25,17,30,0), EndDateTime =  new DateTime(2024,7,25,22,20,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,26,14,20,0), EndDateTime =  new DateTime(2024,7,26,23,15,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,27,10,30,0), EndDateTime =  new DateTime(2024,7,27,15,16,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,30,20,0,0), EndDateTime =  new DateTime(2024,7,30,23,20,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,6,20,0,0), EndDateTime =  new DateTime(2024,7,6,23,44,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,8,20,0,0), EndDateTime =  new DateTime(2024,7,8,23,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,11,23,0,0), EndDateTime =  new DateTime(2024,7,12,1,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,16,22,0,0), EndDateTime =  new DateTime(2024,7,16,23,40,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,18,17,40,0), EndDateTime =  new DateTime(2024,7,18,21,55,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,21,18,0,0), EndDateTime =  new DateTime(2024,7,21,22,0,0), AttendanceLogEmployeeShift = shiftJson,},
                new AttendanceLogReport(){ StartDateTime = new DateTime(2024,7,22,17,30,0), EndDateTime =  new DateTime(2024,7,22,21,15,0), AttendanceLogEmployeeShift = shiftJson,},
            };

            foreach (var attendanceLogReport in attendanceLogReports)
            {
                attendanceLogReport.TotalSeconds = ValidateValue.ValidateLong((attendanceLogReport.EndDateTime - attendanceLogReport.StartDateTime).TotalSeconds);
                attendanceLogReport.OrganizationId = Ulid.Parse("01JNYBT1RSCSHEEZHZ9KGE1Q9Q");
                attendanceLogReport.EmployeeId = GetEmployee("01JNVD6Q9XQ0C9MSWFHDS9P2VJ").Id;
                attendanceLogReport.Employee = GetEmployee("01JNVD6Q9XQ0C9MSWFHDS9P2VJ");
            }

            return attendanceLogReports;
        }
    }
}
