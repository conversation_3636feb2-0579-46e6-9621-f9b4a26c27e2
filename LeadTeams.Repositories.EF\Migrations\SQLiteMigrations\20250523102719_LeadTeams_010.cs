﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LeadTeams.Repositories.EF.Migrations.SQLiteMigrations
{
    /// <inheritdoc />
    public partial class LeadTeams_010 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Employee_User_UserId",
                table: "Employee");

            migrationBuilder.AlterColumn<byte[]>(
                name: "UserId",
                table: "Employee",
                type: "BLOB",
                nullable: true,
                oldClrType: typeof(byte[]),
                oldType: "BLOB");

            migrationBuilder.AddForeignKey(
                name: "FK_Employee_User_UserId",
                table: "Employee",
                column: "UserId",
                principalTable: "User",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeign<PERSON>ey(
                name: "FK_Employee_User_UserId",
                table: "Employee");

            migrationBuilder.AlterColumn<byte[]>(
                name: "UserId",
                table: "Employee",
                type: "BLOB",
                nullable: false,
                defaultValue: new byte[0],
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Employee_User_UserId",
                table: "Employee",
                column: "UserId",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
