﻿namespace LeadTeams.API.Controllers.Business
{
    public class ScreensAccessProfileController : BaseBusinessController<ScreensAccessProfileModel, ScreensAccessProfileModel, CreateScreensAccessProfileViewModel, UpdateScreensAccessProfileViewModel>
    {
        private readonly IScreensAccessProfileService _taskService;

        public ScreensAccessProfileController(IScreensAccessProfileService taskService) : base(taskService)
        {
            _taskService = taskService;
        }
    }
}
