﻿namespace LeadTeams.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class BaseBusinessController<TEntity, TEntityView, TEntityCreate, TEntityUpdate> : ControllerBase
        where TEntity : BaseIdentityModel
        where TEntityView : BaseIdentityModel
        where TEntityCreate : BaseOrganizationCreateViewModel, IEntityMapper<TEntity, TEntityCreate>
        where TEntityUpdate : BaseOrganizationUpdateViewModel, IEntityMapper<TEntity, TEntityUpdate>
    {
        protected readonly ILeadTeamsBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> _leadTeamsBaseService;

        public BaseBusinessController(ILeadTeamsBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> leadTeamsBaseService)
        {
            _leadTeamsBaseService = leadTeamsBaseService;
        }

        [HttpGet("GetAllAsync")]
        public async Task<IActionResult> GetAllAsync()
        {
            try
            {
                var result = await _leadTeamsBaseService.GetAllAsync();
                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<List<TEntity>>(ex.Message);
            }
        }

        [HttpGet("GetAllPaginationListAsync")]
        public async Task<IActionResult> GetAllPaginationListAsync(int pageNumber, int pageSize = 25)
        {
            try
            {
                if (pageNumber < 1 || pageSize < 1)
                    return this.ApiFailure<PaginationList<TEntity>>("PageNumber and PageSize must be greater than 0.");

                var result = await _leadTeamsBaseService.GetAllPaginationListAsync(pageNumber, pageSize);
                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<PaginationList<TEntity>>(ex.Message);
            }
        }

        [HttpGet("GetAllByNameAsync")]
        public async Task<IActionResult> GetAllByNameAsync(string name, int pageNumber, int pageSize = 25)
        {
            try
            {
                if (string.IsNullOrEmpty(name))
                    return this.ApiFailure<PaginationList<TEntity>>("Name must be filled.");
                if (pageNumber < 1 || pageSize < 1)
                    return this.ApiFailure<PaginationList<TEntity>>("PageNumber and PageSize must be greater than 0.");

                var result = await _leadTeamsBaseService.GetAllByNameAsync(name, pageNumber, pageSize);
                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<PaginationList<TEntity>>(ex.Message);
            }
        }

        [HttpGet("GetAllViewAsync")]
        public async Task<IActionResult> GetAllViewAsync(int pageNumber, int pageSize = 25)
        {
            try
            {
                if (pageNumber < 1 || pageSize < 1)
                    return this.ApiFailure<PaginationList<TEntityView>>("PageNumber and PageSize must be greater than 0.");

                var result = await _leadTeamsBaseService.GetAllViewAsync(pageNumber, pageSize);
                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<PaginationList<TEntityView>>(ex.Message);
            }
        }

        [HttpGet("GetAllViewByAsync")]
        public async Task<IActionResult> GetAllViewByAsync(string searchValuesJson, int pageNumber, int pageSize = 25)
        {
            try
            {
                TEntityView? searchValues = Shared.Helper.JsonUtilities.ReadFromJsonString<TEntityView>(searchValuesJson);

                if (searchValues is null)
                    return this.ApiFailure<PaginationList<TEntityView>>("Search Values must be filled.");
                if (pageNumber < 1 || pageSize < 1)
                    return this.ApiFailure<PaginationList<TEntityView>>("PageNumber and PageSize must be greater than 0.");

                var result = await _leadTeamsBaseService.GetAllViewByAsync(searchValues, pageNumber, pageSize);
                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<PaginationList<TEntityView>>(ex.Message);
            }
        }

        [HttpGet("GetSelectListAsync")]
        public async Task<IActionResult> GetSelectListAsync(Expression<Func<TEntity, bool>>? searchValue = null)
        {
            try
            {
                var result = await _leadTeamsBaseService.GetSelectListAsync(searchValue);
                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<IEnumerable<object>>(ex.Message);
            }
        }

        [HttpGet("GetByIdAsync")]
        public async Task<IActionResult> GetByIdAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _leadTeamsBaseService.GetByIdAsync(ulid);
                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<TEntity>(ex.Message);
            }
        }

        [HttpGet("IsExistAsync")]
        public async Task<IActionResult> IsExistAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _leadTeamsBaseService.IsExistAsync(ulid);
                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<bool>(ex.Message);
            }
        }

        [HttpPost("AddAsync")]
        public async Task<IActionResult> AddAsync([FromBody] TEntityCreate model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return this.ApiValidationFailure<TEntity>(ModelState);

                var result = await _leadTeamsBaseService.AddAsync(model);

                if (result is null)
                    return this.ApiFailure<TEntity>("Failed to create entity. Please check your data and try again.");

                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<TEntity>(ex.Message);
            }
        }

        [HttpPut("UpdateAsync")]
        public async Task<IActionResult> UpdateAsync([FromBody] TEntityUpdate model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return this.ApiValidationFailure<TEntity>(ModelState);

                var result = await _leadTeamsBaseService.UpdateAsync(model);

                if (result is null)
                    return this.ApiFailure<TEntity>("Failed to update entity. Please check your data and try again.");

                return this.ApiSuccess(result);
            }
            catch (Exception ex)
            {
                return this.ApiFailure<TEntity>(ex.Message);
            }
        }

        [HttpDelete("RemoveAsync/{id}")]
        public async Task<IActionResult> RemoveAsync([FromRoute] string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _leadTeamsBaseService.RemoveAsync(ulid);

                if (!result.IsSuccess)
                    return this.ApiFailure("Failed to delete entity. It may not exist.");

                return this.ApiSuccess("Entity deleted successfully.");
            }
            catch (Exception ex)
            {
                return this.ApiFailure(ex.Message);
            }
        }
    }
}
