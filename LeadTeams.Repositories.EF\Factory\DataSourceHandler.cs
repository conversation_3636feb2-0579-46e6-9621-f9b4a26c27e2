﻿namespace LeadTeams.Repositories.EF.Factory
{
    public class DataSourceHandler : IDataSourceHandler
    {
        private IUnitOfWork repository;

        public DataSourceHandler(IUnitOfWork repository)
        {
            this.repository = repository;
        }

        public object SetDataSource(string name)
        {
            string cleanedName = GetCleanName(name);

            switch (cleanedName)
            {
                case var _ when cleanedName == GetCleanName(nameof(AttendanceLogModel)):
                    return repository.AttendanceLog.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(EmployeeEducationalQualificationModel)):
                    return repository.EmployeeEducationalQualification.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(EmployeeKidsModel)):
                    return repository.EmployeeKids.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(EmployeeModel)):
                    return repository.Employee.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(MeetingModel)):
                    return repository.Meeting.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(MessageModel)):
                    return repository.Message.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(OrganizationModel)):
                    return repository.Organization.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(OrganizationNewsModel)):
                    return repository.OrganizationNews.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(ProjectModel)):
                    return repository.Project.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(ScreensAccessProfileModel)):
                    return repository.ScreensAccessProfile.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(ScreenShotsMonitoringModel)):
                    return repository.ScreenShotsMonitoring.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(ShiftModel)):
                    return repository.Shift.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(TaskModel)):
                    return repository.Task.GetAsSelectedItems();
                case var _ when cleanedName == GetCleanName(nameof(CurrencyEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<CurrencyEnumeration>();
                case var _ when cleanedName == GetCleanName(nameof(MaritalStatusEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<MaritalStatusEnumeration>();
                case var _ when cleanedName == GetCleanName(nameof(GenderEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<GenderEnumeration>();
                case var _ when cleanedName == GetCleanName(nameof(TaskPriorityEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<TaskPriorityEnumeration>();
                case var _ when cleanedName == GetCleanName(nameof(TaskStatusEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<TaskStatusEnumeration>();
                default:
                    throw new NotImplementedException($"Need to implement the datasource for {name}");
            }
        }

        public string SetDisplayMember(string name)
        {
            return nameof(IdAndName.Name);
        }

        public string SetValueMember(string name)
        {
            string cleanedName = GetCleanName(name);

            switch (cleanedName)
            {
                case var _ when cleanedName == GetCleanName(nameof(CurrencyEnumeration)):
                case var _ when cleanedName == GetCleanName(nameof(MaritalStatusEnumeration)):
                case var _ when cleanedName == GetCleanName(nameof(GenderEnumeration)):
                case var _ when cleanedName == GetCleanName(nameof(TaskPriorityEnumeration)):
                case var _ when cleanedName == GetCleanName(nameof(TaskStatusEnumeration)):
                    return nameof(IdAndName.Name);
                default:
                    return nameof(IdAndName.Id);
            }
        }

        private string GetCleanName(string value) => ValidateValue.GetCleanName(value);
    }
}
