﻿namespace LeadTeams.API.Extensions
{
    /// <summary>
    /// Extension methods for creating API results
    /// </summary>
    public static class ServiceResultExtensions
    {
        /// <summary>
        /// Creates a successful API result
        /// </summary>
        /// <typeparam name="T">The type of data being returned</typeparam>
        /// <param name="controller">The controller</param>
        /// <param name="data">The data to return</param>
        /// <param name="message">An optional success message</param>
        /// <returns>An IActionResult containing a successful ServiceResult</returns>
        public static IActionResult ApiSuccess<T>(this ControllerBase controller, T data, string message = "Operation completed successfully")
        {
            var result = ServiceResult<T>.Success(data, message);
            return controller.StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a successful API result with no data
        /// </summary>
        /// <param name="controller">The controller</param>
        /// <param name="message">An optional success message</param>
        /// <returns>An IActionResult containing a successful ServiceResult</returns>
        public static IActionResult ApiSuccess(this ControllerBase controller, string message = "Operation completed successfully")
        {
            var result = ServiceResult.Success(message);
            return controller.StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a successful API result from an existing ServiceResult
        /// </summary>
        /// <typeparam name="T">The type of data being returned</typeparam>
        /// <param name="controller">The controller</param>
        /// <param name="result">The ServiceResult to return</param>
        /// <returns>An IActionResult containing the ServiceResult</returns>
        public static IActionResult ApiSuccess<T>(this ControllerBase controller, ServiceResult<T> result)
        {
            return controller.StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a successful API result from an existing ServiceResult with no data type
        /// </summary>
        /// <param name="controller">The controller</param>
        /// <param name="result">The ServiceResult to return</param>
        /// <returns>An IActionResult containing the ServiceResult</returns>
        public static IActionResult ApiSuccess(this ControllerBase controller, GMCadiomCore.Models.ResultPattern.ServiceResult result)
        {
            return controller.StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a failure API result
        /// </summary>
        /// <typeparam name="T">The type of data being returned</typeparam>
        /// <param name="controller">The controller</param>
        /// <param name="message">The error message</param>
        /// <param name="statusCode">The HTTP status code</param>
        /// <returns>An IActionResult containing a failure ServiceResult</returns>
        public static IActionResult ApiFailure<T>(this ControllerBase controller, string message, HttpStatusCode statusCode = HttpStatusCode.BadRequest)
        {
            var result = ServiceResult<T>.Failure(message, statusCode);
            return controller.StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a failure API result with no data type
        /// </summary>
        /// <param name="controller">The controller</param>
        /// <param name="message">The error message</param>
        /// <param name="statusCode">The HTTP status code</param>
        /// <returns>An IActionResult containing a failure ServiceResult</returns>
        public static IActionResult ApiFailure(this ControllerBase controller, string message, HttpStatusCode statusCode = HttpStatusCode.BadRequest)
        {
            var result = ServiceResult.Failure(message, statusCode);
            return controller.StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a validation failure API result
        /// </summary>
        /// <typeparam name="T">The type of data being returned</typeparam>
        /// <param name="controller">The controller</param>
        /// <param name="modelState">The ModelState containing validation errors</param>
        /// <param name="message">An optional error message</param>
        /// <returns>An IActionResult containing a validation failure ServiceResult</returns>
        public static IActionResult ApiValidationFailure<T>(this ControllerBase controller, ModelStateDictionary modelState, string message = "Validation failed")
        {
            var validationErrors = modelState
                .Where(x => x.Value != null && x.Value.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value!.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            var result = ServiceResult<T>.ValidationFailure(validationErrors, message);
            return controller.StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a validation failure API result with no data type
        /// </summary>
        /// <param name="controller">The controller</param>
        /// <param name="modelState">The ModelState containing validation errors</param>
        /// <param name="message">An optional error message</param>
        /// <returns>An IActionResult containing a validation failure ServiceResult</returns>
        public static IActionResult ApiValidationFailure(this ControllerBase controller, ModelStateDictionary modelState, string message = "Validation failed")
        {
            var validationErrors = modelState
                .Where(x => x.Value != null && x.Value.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value!.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            var result = ServiceResult.ValidationFailure(validationErrors, message);
            return controller.StatusCode((int)result.StatusCode, result);
        }
    }
}
