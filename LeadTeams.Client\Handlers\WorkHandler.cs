﻿namespace LeadTeams.Client.Handlers
{
    internal class WorkHandler
    {
        private readonly ISession _session;
        private readonly IEmployeeTrackerView _view;
        private readonly IServices _services;
        private readonly IAttendanceLogService _attendanceLogService;
        private readonly ITaskService _taskService;
        private readonly IAttendanceLogger _attendanceLogger;
        private readonly IPopupService _popupService;
        private readonly System.Timers.Timer _reminderTimer;
        private int _checkingSnoozeCounter;
        private int _maxSnooze;

        internal WorkHandler(ISession session, IEmployeeTrackerView view, IServices services, IAttendanceLogService attendanceLogService, ITaskService taskService, IAttendanceLogger attendanceLogger, IPopupService popupService)
        {
            _session = session;
            _session.AppLogger.LogInformation($"Start presenter");
            _session.AppLogger.LogInformation($"Setting variables");
            _view = view;
            _services = services;
            _attendanceLogService = attendanceLogService;
            _taskService = taskService;
            _attendanceLogger = attendanceLogger;
            _popupService = popupService;
            _reminderTimer = new System.Timers.Timer(1000);

            _session.AppLogger.LogInformation($"Setting configs");
            _reminderTimer.Elapsed += StopReminderHandlerAsync;
        }

        internal async Task StartWorkingLastTask()
        {
            try
            {
                var SnoozeDialogResult = _popupService.ShowSnoozePopUp();
                _session.AppLogger.LogDebug($"Handle SnoozeDialogResult action [{SnoozeDialogResult.Item1},{SnoozeDialogResult.Item2}]");
                Enums.SnoozePopUpResult SnoozePopUpResult = SnoozeDialogResult.Item1;
                switch (SnoozePopUpResult)
                {
                    case Enums.SnoozePopUpResult.Snooze:
                        int? SnoozeTime = SnoozeDialogResult.Item2;
                        if (SnoozeTime == null)
                            return;
                        _maxSnooze = SnoozeTime.Value;
                        _session.AppLogger.LogDebug($"Start ReminderTimer");
                        _reminderTimer.Start();
                        break;
                    case Enums.SnoozePopUpResult.StartWork:
                        AttendanceLogModel? attendanceLog = _attendanceLogService.GetLastAttendanceLog(_session.Employee.Id);

                        if (attendanceLog == null)
                            return;

                        if (attendanceLog.TaskId != null)
                            _view.TaskModel = _taskService.GetTask(attendanceLog.TaskId);

                        _session.AppLogger.LogDebug($"Start working again");
                        await BeginWorking(true);
                        break;
                    case Enums.SnoozePopUpResult.Shutdown:
                        _session.AppLogger.LogDebug($"Shutdown the application");
                        _view.CloseView();
                        break;
                }
            }
            catch (Exception ex)
            {
                _view.Message = ex.Message;
                _session.AppLogger.LogError(ex, ex.Message);
            }
        }

        internal async Task StartWorking()
        {
            await BeginWorking(false);
        }

        private async Task BeginWorking(bool isStartUp)
        {
            _session.AppLogger.LogDebug($"BeginWorking with startup is [{isStartUp}]");

            if (_view.TaskModel == null)
            {
                if (!isStartUp)
                {
                    _session.AppLogger.LogDebug($"Need To Define Task First");
                    _view.Message = "Need To Define Task First";
                }
                return;
            }

            if (_view.MonitoringStatus == Enums.MonitoringStatus.WorkingOnTask)
                return;

            _session.AppLogger.LogDebug($"BeginWorking with startup is [{isStartUp}]");

            _view.IsWorking = true;
            _services.IsOverTime = _services.StopwatchWorkingTime.Elapsed + _services.WorkedTime >= _services.TargetTime;
            if (_services.IsOverTime)
                _services.StopwatchOverTime.Start();
            else
                _services.StopwatchWorkingTime.Start();
            _view.MonitoringStatus = Enums.MonitoringStatus.WorkingOnTask;
            await _attendanceLogger.SaveAttendanceLogAsync(_view.TaskModel, _view.MonitoringStatus);
        }

        private void StopReminderHandlerAsync(object? sender, System.Timers.ElapsedEventArgs e)
        {
            _ = HandleReminderTimerAsync();
        }

        private async Task HandleReminderTimerAsync()
        {
            try
            {
                if (_view.MonitoringStatus == Enums.MonitoringStatus.WorkingOnTask)
                {
                    _session.AppLogger.LogDebug($"Task has started and stop reminder timer");
                    _reminderTimer.Stop();
                    _checkingSnoozeCounter = 0;
                    _view.Test5 = $"{_checkingSnoozeCounter} - {_maxSnooze}";
                    return;
                }

                _checkingSnoozeCounter++;
                _view.Test5 = $"{_checkingSnoozeCounter} - {_maxSnooze}";

                if (_checkingSnoozeCounter >= _maxSnooze)
                {
                    _reminderTimer.Stop();
                    _checkingSnoozeCounter = 0;
                    await Task.CompletedTask; // Ensure it's properly awaited
                }
            }
            catch (Exception ex)
            {
                _view.Message = ex.Message;
                _session.AppLogger.LogError(ex, ex.Message);
            }
        }
    }
}
