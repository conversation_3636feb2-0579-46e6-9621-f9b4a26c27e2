﻿namespace LeadTeams.Models.Model
{
    [Table("ShiftDynamicPattern")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ShiftDynamicPatternModel>))]
    public class ShiftDynamicPatternModel : BaseOrganizationWithTrackingModel
    {
        private int _DayOfWeek;
        private TimeSpan _TargetTime = DateTime.Now.TimeOfDay;
        private Ulid _ShiftId;

        public ShiftDynamicPatternModel()
        {
        }

        public ShiftDynamicPatternModel(Ulid id, int dayOfWeek, TimeSpan targetTime, Ulid shiftId, Ulid organizationId)
        {
            Id = id;
            DayOfWeek = dayOfWeek;
            TargetTime = targetTime;
            ShiftId = shiftId;
            OrganizationId = organizationId;
        }

        [CustomRequired]
        [DisplayName("Day Of Week")]
        public int DayOfWeek { get => _DayOfWeek; set => this.CheckPropertyChanged(ref _DayOfWeek, ref value); }
        [DisplayName("Target Time")]
        [Column(TypeName = "Time"), DataType(DataType.Time)]
        public TimeSpan TargetTime { get => _TargetTime; set => this.CheckPropertyChanged(ref _TargetTime, ref value); }

        [CustomRequired]
        [Browsable(false)]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(ShiftModel))]
        [DisplayName("Shift")]
        public Ulid ShiftId { get => _ShiftId; set => this.CheckPropertyChanged(ref _ShiftId, ref value); }
        [Browsable(false)]
        public ShiftModel Shift { get; set; } = null!;
    }
}
