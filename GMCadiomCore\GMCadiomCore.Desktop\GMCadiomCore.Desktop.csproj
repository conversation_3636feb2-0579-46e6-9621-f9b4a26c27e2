﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
	<UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.104.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GMCadiomCore.Authentications\GMCadiomCore.Authentications.csproj" />
    <ProjectReference Include="..\GMCadiomCore.Desktop.SyncTools\GMCadiomCore.Desktop.SyncTools.csproj" />
    <ProjectReference Include="..\GMCadiomCore.Extensions.IEnumerable\GMCadiomCore.Extensions.IEnumerable.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="CustomControls\cmb\cmbBindingComboBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="CustomControls\cmb\cmbIdAndName.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CustomControls\cmb\MultiColumnComboBox\FormSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="CustomControls\cmb\MultiColumnComboBox\MultiColumnComboBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="CustomControls\dgv\dgvTotalsSummary.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CustomControls\dgv\dgvViewList.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CustomControls\dgv\frmColumnsVisibility.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="CustomControls\dgv\SummaryDataGridView\DataGridViewSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="CustomControls\dgv\SummaryDataGridView\ReadOnlyTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="CustomControls\dgv\SummaryDataGridView\SummaryControlContainer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="CustomControls\dtp\dtpBindingDateTimePicker.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Shared\Helper\SyncBindingSource.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="View\BaseView.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>

</Project>
