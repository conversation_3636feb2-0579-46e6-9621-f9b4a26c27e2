﻿namespace LeadTeams.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SignalRController : ControllerBase
    {
        private readonly ILogger<SignalRController> _logger;
        private readonly IHubContext<LeadTeamsHub> _hubContext;
        private readonly IRepository _repository;

        public SignalRController(ILogger<SignalRController> logger, IHubContext<LeadTeamsHub> hubContext, IRepository repository)
        {
            _logger = logger;
            _hubContext = hubContext;
            _repository = repository;
        }

        [HttpGet("GetConnectedClients")]
        public async Task<IActionResult> GetConnectedClients()
        {
            try
            {
                var connectedClients = await _repository.GetClientsByType<Client, ClientDto>();
                _logger.LogInformation($"Connected Clients Count Are : {connectedClients.Count}");
                return Ok(connectedClients);
            }
            catch (Exception ex)
            {
                return this.NotFound(ex);
            }
        }

        [HttpGet("SendDatabaseChanges")]
        public async Task<IActionResult> SendDatabaseChanges()
        {
            try
            {
                await _hubContext.Clients.All.SendAsync(SignalRChatIntegrationHelper.HUBs.OnDatabaseChanges, null);
                _logger.LogInformation($"Ping All Connected Employees DatabaseChanges");
                return await Task.FromResult(Ok());
            }
            catch (Exception ex)
            {
                return await Task.FromResult(this.NotFound(ex));
            }
        }
    }
}
