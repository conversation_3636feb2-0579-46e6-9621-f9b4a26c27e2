namespace LeadTeams.Repositories.EF.Repository
{
    internal class AttendanceLogRepository : LeadTeamsBaseRepository<AttendanceLogModel, AttendanceLogViewModel>, IAttendanceLogRepository<AttendanceLogModel, AttendanceLogViewModel>
    {
        public AttendanceLogRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override IQueryable<AttendanceLogViewModel> ViewQueryable => AttendanceLogQueries.AttendanceLogViewQuery(_context);
        protected override string ViewQuery => AttendanceLogQueries.AttendanceLogViewQuery(_context).ToQueryString();
        protected override Expression<Func<AttendanceLogViewModel, object>> OrderByColumn => x => x.Id;

        public List<AttendanceLogReport> GetAttendanceLogByDaily(Ulid organizationId, Ulid? employeeId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            string sql = AttendanceLogQueries.GetAttendanceLogByDaily(organizationId, employeeId, startDate, endDate);

            IQueryable<AttendanceLogReport> query = _context.Database.SqlQueryRaw<AttendanceLogReport>(sql).AsNoTracking();

            return query.ToList();
        }

        public List<AttendanceLogReport> GetAttendanceLogByWeekly(Ulid organizationId, Ulid? employeeId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            string sql = AttendanceLogQueries.GetAttendanceLogByWeekly(organizationId, employeeId, startDate, endDate);

            IQueryable<AttendanceLogReport> query = _context.Database.SqlQueryRaw<AttendanceLogReport>(sql).AsNoTracking();

            return query.ToList();
        }

        public List<AttendanceLogReport> GetAttendanceLogByMonthly(Ulid organizationId, Ulid? employeeId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            string sql = AttendanceLogQueries.GetAttendanceLogByMonthly(organizationId, employeeId, startDate, endDate);

            IQueryable<AttendanceLogReport> query = _context.Database.SqlQueryRaw<AttendanceLogReport>(sql).AsNoTracking();

            return query.ToList();
        }

        public TimeSpan GetEmployeeWorkingByDay(Ulid organizationId, Ulid employeeId, DateTime day)
        {
            DateTime startDate = day;
            DateTime endDate = day.AddDays(1).AddSeconds(-1);

            var result = GetAttendanceLogByDaily(organizationId, employeeId, startDate, endDate);
            long totalSeconds = result.Sum(x => x.TotalSeconds);
            var total = new TimeSpan(0, 0, (int)totalSeconds);
            return total;
        }

        public TimeSpan GetEmployeeWorkingByMonth(Ulid organizationId, Ulid employeeId, DateTime month)
        {
            DateTime startDate = new DateTime(month.Year, month.Month, 1);
            DateTime endDate = new DateTime(month.Year, month.Month + 1, 1).AddSeconds(-1);

            var result = GetAttendanceLogByMonthly(organizationId, employeeId, startDate, endDate);
            long totalSeconds = result.Sum(x => x.TotalSeconds);
            var total = new TimeSpan(0, 0, (int)totalSeconds);
            return total;
        }

        public AttendanceLogModel? GetLastAttendanceLog(Ulid employeeId)
        {
            RepositorySpecifications<AttendanceLogModel> repositorySpecifications = new RepositorySpecifications<AttendanceLogModel>()
            {
                SearchValue = x => x.EmployeeId == employeeId,
            };

            IQueryable<AttendanceLogModel> query = GetQueryable(repositorySpecifications);
            AttendanceLogModel? attendanceLog = query.OrderBy(x => x.AttendanceLogDateTime).LastOrDefault();
            return attendanceLog;
        }
    }
}
