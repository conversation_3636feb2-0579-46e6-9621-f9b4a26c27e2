﻿namespace LeadTeams.Services.Server.Reports
{
    public class PayrollService : IPayrollService
    {
        private readonly IAttendanceService _attendanceService;
        private readonly IEmployeeService _employeeService;
        private readonly IShiftService _shiftService;

        public PayrollService(IAttendanceService attendanceService, IEmployeeService employeeService, IShiftService shiftService)
        {
            _attendanceService = attendanceService;
            _employeeService = employeeService;
            _shiftService = shiftService;
        }

        public List<PayrollReport> GetPayrollReports(Ulid organizationId, Ulid? employeeId = null, DateTime? from = null, DateTime? to = null)
        {
            DateTime startDate = from ?? new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month, 1);
            DateTime endDate = to ?? new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month + 1, 1).AddSeconds(-1);

            string groupBy = TargetTimeDurationEnumeration.Monthly.Name;
            List<AttendanceLogReport> attendanceLogReports = _attendanceService.GetEmployeeAttendanceLog(groupBy, organizationId, employeeId, startDate, endDate);

            List<PayrollReport> payrollReports = GetPayrollReports(organizationId, attendanceLogReports, startDate, endDate);

            return payrollReports;
        }

        public List<PayrollReport> GetPayrollReports(Ulid organizationId, IEnumerable<AttendanceLogReport> attendanceLogReports, DateTime? from = null, DateTime? to = null)
        {
            DateTime startDate = from ?? new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month, 1);
            DateTime endDate = to ?? new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month + 1, 1).AddSeconds(-1);

            List<PayrollReport> payrollReports = new List<PayrollReport>();

            var employeeIds = attendanceLogReports.Select(x => x.EmployeeId).Distinct().ToList();
            var employees = _employeeService.GetEmployees(employeeIds, organizationId);

            foreach (AttendanceLogReport attendanceLogReport in attendanceLogReports)
            {
                RepositorySpecifications<EmployeeModel> repositorySpecifications = new RepositorySpecifications<EmployeeModel>()
                {
                    SearchValue = x => x.OrganizationId == organizationId && x.Id == attendanceLogReport.EmployeeId,
                    Includes = x => x.Include(xx => xx.Shift).ThenInclude(xx => xx.ShiftFixedPatterns).Include(xx => xx.EmployeeAllowances),
                    IsTackable = false,
                };

                employees.TryGetValue(attendanceLogReport.EmployeeId, out var employee);

                EmployeeModel? employeeModel = attendanceLogReport.Employee ?? employee;

                if (employeeModel == null) continue;

                ShiftDto? shift = attendanceLogReport.Shift;
                if (shift == null) continue;

                IEnumerable<EmployeeAllowanceModel> employeeAllowances = employeeModel.EmployeeAllowances;

                decimal employeeAllowancesAmount = GetEmployeeAllowance(employeeAllowances, startDate);

                TimeSpan targetHours = _shiftService.GetTargetHours(shift, startDate, endDate);
                if (targetHours == TimeSpan.Zero) continue;

                TimeSpan attendanceLogDuration = new TimeSpan(0, 0, (int)attendanceLogReport.TotalSeconds);
                if (attendanceLogDuration == TimeSpan.Zero) continue;

                decimal hourRate = decimal.Round(employeeModel.EmployeeSalary / (decimal)targetHours.TotalHours, 2, MidpointRounding.AwayFromZero);
                decimal attendanceLogAmount = decimal.Round(hourRate * (decimal)attendanceLogDuration.TotalHours, 2, MidpointRounding.AwayFromZero);

                payrollReports.Add(new PayrollReport()
                {
                    AttendanceLogDuration = attendanceLogDuration,
                    TargetHours = targetHours,
                    Salary = decimal.Round(employeeModel.EmployeeSalary, 2, MidpointRounding.AwayFromZero),
                    AllowanceAmount = employeeAllowancesAmount,
                    AttendanceLogAmount = attendanceLogAmount,
                    OrganizationId = attendanceLogReport.OrganizationId,
                    EmployeeId = attendanceLogReport.EmployeeId,
                    EmployeeName = employeeModel.EmployeeName,
                    Currency = employeeModel.EmployeeCurrency,
                });
            }

            List<PayrollReport> groupedList = payrollReports.GroupBy(x => new { x.OrganizationId, x.EmployeeId, x.Salary }).Select(x => new PayrollReport()
            {
                EmployeeId = x.Key.EmployeeId,
                OrganizationId = x.Key.OrganizationId,
                Salary = x.Key.Salary,
                EmployeeName = x.First().EmployeeName,
                AllowanceAmount = x.First().AllowanceAmount,
                AttendanceLogAmount = x.Sum(xx => xx.AttendanceLogAmount),
                AttendanceLogDuration = new TimeSpan(x.Sum(xx => xx.AttendanceLogDuration.Ticks)),
                TargetHours = new TimeSpan(x.Sum(xx => xx.TargetHours.Ticks)),
                Currency = x.First().Currency,
            }).ToList();

            return groupedList;
        }

        private decimal GetEmployeeAllowance(IEnumerable<EmployeeAllowanceModel> employeeAllowances, DateTime? from = null)
        {
            DateTime startDate = from ?? new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month, 1);

            decimal employeeAllowancesAmount = 0;
            int daysInMonth = DateTime.DaysInMonth(startDate.Year, startDate.Month);
            int weeksInMonth = (int)Math.Ceiling(daysInMonth / 7.0);

            foreach (var item in employeeAllowances)
            {
                switch (item.Allowance.EmployeeAllowanceDuration)
                {
                    case nameof(EmployeeAllowanceDurationEnumeration.Daily):
                        employeeAllowancesAmount += daysInMonth * item.EmployeeAllowanceAmount;
                        break;
                    case nameof(EmployeeAllowanceDurationEnumeration.Weekly):
                        employeeAllowancesAmount += weeksInMonth * item.EmployeeAllowanceAmount;
                        break;
                    case nameof(EmployeeAllowanceDurationEnumeration.Monthly):
                        employeeAllowancesAmount += item.EmployeeAllowanceAmount;
                        break;
                    case nameof(EmployeeAllowanceDurationEnumeration.Yearly):
                        employeeAllowancesAmount += item.EmployeeAllowanceAmount / 12;
                        break;
                }
            }

            return employeeAllowancesAmount;
        }
    }

    public static class PayrollServiceTest
    {
        public static List<PayrollReport> GetTestData(IAuthenticationValidationService authenticationValidationService, IAttendanceService attendanceService, IEmployeeService employeeService, IShiftService shiftService)
        {
            PayrollService payrollService = new PayrollService(attendanceService, employeeService, shiftService);

            List<AttendanceLogReport> attendanceLogReports = AttendanceServiceTest.GetAttendanceLogReports();

            DateTime startDate = new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month, 1);
            DateTime endDate = new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month + 1, 1).AddSeconds(-1);

            SessionIdentity sessionIdentity = authenticationValidationService.GetSessionIdentity();
            List<PayrollReport> payrollReports = payrollService.GetPayrollReports(sessionIdentity.OrganizationId, attendanceLogReports, startDate, endDate);

            return payrollReports;
        }
    }
}
