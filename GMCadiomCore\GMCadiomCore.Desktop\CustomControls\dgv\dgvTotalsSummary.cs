﻿namespace GMCadiomCore.Desktop.CustomControls.dgv
{
    public partial class dgvTotalsSummary : UserControl, ISupportInitialize
    {
        private TableLayoutPanel tlpSummaries;
        private BindingList<DataGridViewColumn> columns;
        private Dictionary<int, Label> SummaryFields;
        private bool IsNeedToReInitializeSummaryFields => columns?.Count != dgvMain.Columns.Cast<DataGridViewColumn>().Where(x => x.Visible).Count();

        private string[] summaryColumns;
        [Browsable(true), Category("Summary")]
        public string[] SummaryColumns
        {
            get { return summaryColumns; }
            set { summaryColumns = value; }
        }

        public object DataSource
        {
            get { return dgvMain.DataSource; }
            set { dgvMain.DataSource = value; }
        }

        public DataGridView DGV => this.dgvMain;

        public dgvTotalsSummary()
        {
            InitializeComponent();
            AssociateAndRaiseEvents();
        }

        private void AssociateAndRaiseEvents()
        {
            dgvMain.DataBindingComplete += (s, e) => InitializeSummaryFields();
            dgvMain.DataError += (s, e) => { };
        }

        private void InitializeSummaryFields()
        {
            if (SummaryColumns == null) return;

            if (!IsNeedToReInitializeSummaryFields)
            {
                UpdateSummaryFieldsValue();
                return;
            }

            tlpMain.RowCount = 2;
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 50F));
            tlpMain.Controls.Add(tlpSummaries, 0, 1);

            // 
            // tlpSummaries
            // 
            tlpSummaries = new TableLayoutPanel();
            tlpSummaries.ColumnCount = 1;
            tlpSummaries.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tlpSummaries.Dock = DockStyle.Fill;
            tlpSummaries.RowCount = 1;
            tlpSummaries.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            tlpSummaries.TabIndex = 0;

            SummaryFields = new Dictionary<int, Label>();
            columns = dgvMain.Columns.Cast<DataGridViewColumn>().Where(x => x.Visible).ToBindingList();

            tlpSummaries.Controls.Clear();
            tlpSummaries.ColumnStyles.Clear();
            tlpSummaries.ColumnCount = columns.Count;
            for (int i = 0; i < tlpSummaries.ColumnCount; i++)
                tlpSummaries.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));

            foreach (DataGridViewColumn column in columns)
            {
                if (SummaryColumns.Contains(column.DataPropertyName) ||
                    SummaryColumns.Contains(column.HeaderText) ||
                    SummaryColumns.Contains(column.Name))
                {
                    switch (Type.GetTypeCode(column.ValueType))
                    {
                        case TypeCode.Int32:
                        case TypeCode.Int16:
                        case TypeCode.Int64:
                        case TypeCode.Single:
                        case TypeCode.Double:
                        case TypeCode.Decimal:
                            {
                                Label summaryLabel = new Label()
                                {
                                    AutoSize = false,
                                    Dock = DockStyle.Fill,
                                    TextAlign = ContentAlignment.MiddleRight,
                                    Text = $"= Σ",
                                };
                                Label summaryValue = new Label()
                                {
                                    Name = column.Name,
                                    AutoSize = false,
                                    Dock = DockStyle.Fill,
                                    TextAlign = ContentAlignment.MiddleCenter,
                                    Text = $"0.00",
                                };
                                summaryValue.Text = dgvMain.Rows.Cast<DataGridViewRow>().Sum(x => ValidateValue.ValidateDecimal(x.Cells[column.Index].Value)).ToString("F2");
                                int index = columns.IndexOf(column);
                                tlpSummaries.Controls.Add(summaryLabel, index, 0);
                                tlpSummaries.Controls.Add(summaryValue, index, 1);
                                SummaryFields.TryAdd(column.Index, summaryLabel);
                            }
                            break;
                        default:
                            continue;
                    }
                }
                else
                    continue;
            }
        }

        private void UpdateSummaryFieldsValue()
        {
            if (SummaryFields.Count == 0) return;

            foreach (var summaryValue in SummaryFields)
                summaryValue.Value.Text = dgvMain.Rows.Cast<DataGridViewRow>().Sum(x => ValidateValue.ValidateDecimal(x.Cells[summaryValue.Key].Value)).ToString("F2");
        }

        public void CreateSummaryRow() => InitializeSummaryFields();

        public void BeginInit()
        {
        }

        public void EndInit()
        {
        }
    }
}
