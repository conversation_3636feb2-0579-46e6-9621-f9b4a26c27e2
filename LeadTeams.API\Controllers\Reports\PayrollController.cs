﻿namespace LeadTeams.API.Controllers.Reports
{
    [Authorize]
    [Route("api/reports/[controller]")]
    [ApiController]
    public class PayrollController : ControllerBase
    {
        private readonly IPayrollService _payrollService;

        public PayrollController(IPayrollService payrollService)
        {
            _payrollService = payrollService;
        }

        [HttpGet("GetPayrollReports")]
        public IActionResult GetPayrollReports(Ulid organizationId, Ulid? employeeId = null, DateTime? from = null, DateTime? to = null)
        {
            try
            {
                var result = _payrollService.GetPayrollReports(organizationId, employeeId, from, to);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
