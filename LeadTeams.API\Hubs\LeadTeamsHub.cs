﻿namespace LeadTeams.API.Hubs
{
    [Authorize]
    public class LeadTeamsHub : SignalRChatHub
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRepository _repository;
        private readonly Serilog.ILogger _logger;

        public LeadTeamsHub(IUnitOfWork unitOfWork, IRepository repository, Serilog.ILogger logger) : base(repository, logger)
        {
            _unitOfWork = unitOfWork;
            _repository = repository;
            _logger = logger;
        }

        public async Task<bool> DatabaseChanges(string dataBaseEntityString)
        {
            await (this as Hub).Clients.All.SendCoreAsync(SignalRChatIntegrationHelper.HUBs.OnDatabaseChanges, new object[] { dataBaseEntityString });
            return await Task.FromResult(true);
        }

        public override async Task<LoginResponse?> Login(SignalRChat.Core.Modules.Login.ViewModel.LoginRequest request)
        {
            LoginResponse? login = await base.Login(request);
            if (login != null)
                foreach (ClientDto client in login.Users)
                {
                    List<MessageModel> messages = _unitOfWork.Message.GetMessagesForEmployee(ValidateValue.ValidateUlid(client.Name));
                    client.ReceivedMessages = (await ConvertToChatMessage(messages)).Where(x => x.ReceiverId == client.ClientId).ToList();
                    client.SentMessages = (await ConvertToChatMessage(messages)).Where(x => x.SenderId == client.ClientId).ToList();
                }
            return login;
        }

        public override async Task<bool> TextMessage(TextMessageRequest request)
        {
            bool result = await base.TextMessage(request);
            if (result)
            {
                var receiverId = await GetClient(request.ReceiverId, false);
                if (receiverId != null)
                {
                    byte[]? message = Converters.ToByteArray(request.Message);
                    if (message != null)
                        await SaveMessageToDatabase(Enums.MessageType.Text, message, ValidateValue.ValidateUlid(receiverId.Name));
                }
            }
            return result;
        }

        public override async Task<bool> ImageMessage(ImageMessageRequest request)
        {
            bool result = await base.ImageMessage(request);
            if (result)
            {
                var receiverId = await GetClient(request.ReceiverId, false);
                if (receiverId != null)
                    await SaveMessageToDatabase(Enums.MessageType.Picture, request.Image, ValidateValue.ValidateUlid(receiverId.Name));
            }
            return result;
        }

        public override async Task<bool> RecordMessage(RecordMessageRequest request)
        {
            bool result = await base.RecordMessage(request);
            if (result)
            {
                var receiverId = await GetClient(request.ReceiverId, false);
                if (receiverId != null)
                    await SaveMessageToDatabase(Enums.MessageType.Record, request.Record, ValidateValue.ValidateUlid(receiverId.Name));
            }
            return result;
        }

        public override async Task<bool> BuzzMessage(BuzzMessageRequest request)
        {
            bool result = await base.BuzzMessage(request);
            if (result)
            {
                var receiverId = await GetClient(request.ReceiverId, false);
                if (receiverId != null)
                    await SaveMessageToDatabase(Enums.MessageType.Buzz, Array.Empty<byte>(), ValidateValue.ValidateUlid(receiverId.Name));
            }
            return result;
        }

        private async Task SaveMessageToDatabase(Enums.MessageType messageType, byte[] messageContent, Ulid employeeToId)
        {
            try
            {
                MessageModel message = new MessageModel()
                {
                    MessageDate = DateTime.Now,
                    MessageType = (int)messageType,
                    MessageContent = messageContent,
                    EmployeeId = employeeToId,
                    MessageMediaMimeType = null,
                    MessageIsRead = false,
                    EmployeeFromId = ValidateValue.ValidateUlid(GetUserName),
                    EmployeeToId = employeeToId,
                    OrganizationId = ValidateValue.ValidateUlid(GetOrganizationId),
                };
                new BaseValidation().Validate(message);
                await _unitOfWork.Message.AddAsync(message);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, ex.Message);
            }
        }

        protected virtual string GetOrganizationId => Context.GetHttpContext()?.Request.Headers["OrganizationId"].FirstOrDefault() ?? throw new NullReferenceException("Organization must not be null");

        private async Task<List<ChatMessageDto>> ConvertToChatMessage(List<MessageModel> messages)
        {
            if (messages == null)
                return new List<ChatMessageDto>();

            List<ChatMessageDto> result = new List<ChatMessageDto>();

            foreach (MessageModel message in messages)
            {
                ClientDto? from = await GetClient(message.EmployeeFromId, false);
                ClientDto? to = await GetClient(message.EmployeeToId, false);

                if (from != null && to != null)
                {
                    SignalRChat.Core.Enums.MessageType messageType = GetMessageType(message.MessageType);

                    ChatMessageDto chatMessageDto = null!;

                    switch (messageType)
                    {
                        case SignalRChat.Core.Enums.MessageType.Text:
                            string? text = Converters.FromByteArray<string>(message.MessageContent);
                            if (text != null)
                                chatMessageDto = new TextChatMessageDto(Ulid.NewUlid(), from.ClientId, to.ClientId, text);
                            break;
                        case SignalRChat.Core.Enums.MessageType.Image:
                            chatMessageDto = new ImageChatMessageDto(Ulid.NewUlid(), from.ClientId, to.ClientId, message.MessageContent);
                            break;
                        case SignalRChat.Core.Enums.MessageType.Record:
                            chatMessageDto = new RecordChatMessageDto(Ulid.NewUlid(), from.ClientId, to.ClientId, message.MessageContent);
                            break;
                        case SignalRChat.Core.Enums.MessageType.Stream:
                        case SignalRChat.Core.Enums.MessageType.Buzz:
                        default:
                            chatMessageDto = new ChatMessageDto(Ulid.NewUlid(), from.ClientId, to.ClientId, message.MessageDate, messageType);
                            break;
                    }
                    if (chatMessageDto != null)
                    {
                        chatMessageDto.SetDateTime(message.MessageDate);
                        result.Add(chatMessageDto);
                    }
                }
            }

            return result;
        }

        private SignalRChat.Core.Enums.MessageType GetMessageType(int messageType)
        {
            switch (messageType)
            {
                case 1: return SignalRChat.Core.Enums.MessageType.Text;
                case 2: return SignalRChat.Core.Enums.MessageType.Image;
                case 3: return SignalRChat.Core.Enums.MessageType.Record;
                case 4: return SignalRChat.Core.Enums.MessageType.Stream;
                case 5: return SignalRChat.Core.Enums.MessageType.Buzz;
                default: return SignalRChat.Core.Enums.MessageType.Text;
            }
        }

        public async Task<Dictionary<string, ClientDto>> GetConnectedClients()
        {
            var users = await _repository.GetClientsByType<Client, ClientDto>();
            return users.Cast<ClientDto>().ToDictionary(x => x.Name);
        }
    }
}
