﻿namespace LeadTeams.Services.API.Factory
{
    public class DataSourceHandler : IDataSourceHandler
    {
        private readonly IServiceProvider _serviceProvider;

        public DataSourceHandler(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public object SetDataSource(string name)
        {
            string cleanedName = GetCleanName(name);

            switch (cleanedName)
            {
                case var _ when cleanedName == GetCleanName(nameof(EmployeeModel)):
                    return _serviceProvider.GetRequiredService<IEmployeeService>().GetSelectList();
                case var _ when cleanedName == GetCleanName(nameof(ProjectModel)):
                    return _serviceProvider.GetRequiredService<IProjectService>().GetSelectList();
                case var _ when cleanedName == GetCleanName(nameof(ShiftModel)):
                    return _serviceProvider.GetRequiredService<IShiftService>().GetSelectList();
                case var _ when cleanedName == GetCleanName(nameof(TaskModel)):
                    return _serviceProvider.GetRequiredService<ITaskService>().GetSelectList();
                case var _ when cleanedName == GetCleanName(nameof(CurrencyEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<CurrencyEnumeration>();
                case var _ when cleanedName == GetCleanName(nameof(MaritalStatusEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<MaritalStatusEnumeration>();
                case var _ when cleanedName == GetCleanName(nameof(GenderEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<GenderEnumeration>();
                case var _ when cleanedName == GetCleanName(nameof(TaskPriorityEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<TaskPriorityEnumeration>();
                case var _ when cleanedName == GetCleanName(nameof(TaskStatusEnumeration)):
                    return EnumerationUtilities.ConvertEnumerationToIdAndNameList<TaskStatusEnumeration>();
                default:
                    throw new NotImplementedException($"Need to implement the datasource for {name}");
            }
        }

        public string SetDisplayMember(string name)
        {
            return nameof(IdAndName.Name);
        }

        public string SetValueMember(string name)
        {
            string cleanedName = GetCleanName(name);

            switch (cleanedName)
            {
                case var _ when cleanedName == GetCleanName(nameof(CurrencyEnumeration)):
                case var _ when cleanedName == GetCleanName(nameof(MaritalStatusEnumeration)):
                case var _ when cleanedName == GetCleanName(nameof(GenderEnumeration)):
                case var _ when cleanedName == GetCleanName(nameof(TaskPriorityEnumeration)):
                case var _ when cleanedName == GetCleanName(nameof(TaskStatusEnumeration)):
                    return nameof(IdAndName.Name);
                default:
                    return nameof(IdAndName.Id);
            }
        }

        private string GetCleanName(string value) => ValidateValue.GetCleanName(value);
    }
}
