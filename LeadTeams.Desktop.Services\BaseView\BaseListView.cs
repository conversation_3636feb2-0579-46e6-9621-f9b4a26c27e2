﻿namespace LeadTeams.Desktop.Services.BaseView
{
    public partial class BaseListView : LeadTeamsForm
    {
        public DataGridView GetDataGridView => dgvMain.GetDgvTotalsSummary.DGV;

        protected IDataSourceHandler DataSourceHandler;
        public string ViewScreenName { get; }

        //For design time support
        public BaseListView() : base()
        {
            InitializeComponent();

            AssociateAndRaiseEvents();

            ShowPrintButtons(IsPrintable);
            ShowImportButtons(IsImportable);
            ShowExportButtons(IsExportable);
            SetIsListOnlyButtons(IsListOnly);

            btnClearFilter.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            btnClearFilter.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            btnClearFilter.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color8;
            btnClearFilter.RadiusProperties.BorderColor = Color.Transparent;
            btnClearFilter.RadiusProperties.BorderRadius = 10;
            btnClearFilter.RadiusProperties.BorderSize = 0;
            lblSearchValues.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font3;
            tlpFilter.Controls.Add(btnClearFilter, 0, 0);
            tlpFilter.Controls.Add(lblSearchValues, 1, 0);

            DataBaseWatcher.OnDataBaseWatcherChanged += OnDataBaseWatcherChanged;
        }

        public BaseListView(string viewScreenName) : this()
        {
            ViewScreenName = viewScreenName;
        }

        public BaseListView(string viewScreenName, PaginationDataGridView paginationList) : this(viewScreenName)
        {
            //Add Custom PaginationDataGridView
            pnlMain.Controls.Remove(dgvMain);
            dgvMain = paginationList;
            dgvMain.Dock = DockStyle.Fill;
            pnlMain.Controls.Add(paginationList);

            //Pagination Events
            ApplyPaginationEvents(dgvMain);
        }

        private void AssociateAndRaiseEvents()
        {
            // Use AsyncButtonHandler to prevent multiple clicks on buttons that trigger potentially long operations
            tsbtnNew.HandleAsyncClickSafe(async () =>
            {
                await Task.Run(() => this.SafelyInvokeAction(() => NewFunction()));
            });

            tsbtnEdit.HandleAsyncClickSafe(async () =>
            {
                if (BaseSession.CheckActionAuthorization(this.Name, Actions.Edit))
                    await Task.Run(() => this.SafelyInvokeAction(() => EditFunction()));
            });

            tsbtnDelete.HandleAsyncClickSafe(async () =>
            {
                if (BaseSession.CheckActionAuthorization(this.Name, Actions.Delete))
                    await Task.Run(() => this.SafelyInvokeAction(() => DeleteFunction()));
            });

            tsbtnRefreshList.HandleAsyncClickSafe(async () =>
            {
                await Task.Run(() => this.SafelyInvokeAction(() => RefreshListFunction()));
            });

            tsbtnPrint.HandleAsyncClickSafe(async () =>
            {
                if (BaseSession.CheckActionAuthorization(this.Name, Actions.Print))
                    await Task.Run(() => this.SafelyInvokeAction(() => PrintFunction()));
            });

            tsbtnFilter.HandleAsyncClickSafe(async () =>
            {
                await Task.Run(() => this.SafelyInvokeAction(() => FilterFunction()));
            });

            btnClearFilter.HandleAsyncClickSafe(async () =>
            {
                await Task.Run(() => this.SafelyInvokeAction(() => ClearFilterFunction()));
            });

            tsbtnImport.HandleAsyncClickSafe(async () =>
            {
                await Task.Run(() => this.SafelyInvokeAction(() => ImportFunction()));
            });

            tsbtnExport.HandleAsyncClickSafe(async () =>
            {
                await Task.Run(() => this.SafelyInvokeAction(() => ExportFunction()));
            });

            tsbtnClose.Click += (s, e) => this.Close();

            //Pagination Events
            ApplyPaginationEvents(dgvMain);
        }

        private void OnDataBaseWatcherChanged(DataBaseEntity dataBaseEntity)
        {
            string t1Name = dgvMain.DataSourceName.Replace("ViewModel", "Model");
            if (t1Name == dataBaseEntity.EntityType)
                HandleDataHasChanged();
        }

        private void HandleDataHasChanged()
        {
            this.SafelyInvokeAction(async () =>
            {
                Color baseColor = tsbtnRefreshList.BackColor;
                // Flashing effect to indicate update
                for (int i = 0; i < 20; i++)
                {
                    tsbtnRefreshList.BackColor = i % 2 == 0 ? Color.Yellow : Color.Orange;
                    await Task.Delay(500); // Pause for 500 milliseconds
                }
                tsbtnRefreshList.BackColor = baseColor; // Reset to default
            });
        }

        #region Buttons Actions Events
        public virtual void NewFunction() { }
        public virtual void EditFunction() { }
        public virtual void DeleteFunction() { }
        public virtual void PrintFunction() { }
        public virtual void RefreshListFunction()
        {
            LoadData(PageNumber, PageSize);
        }
        public virtual void FilterFunction() { }
        public virtual void ClearFilterFunction()
        {
            IsSearched = false;
        }
        public virtual void ImportFunction() { }
        public virtual void ExportFunction()
        {
            Excel.ExportFromDGV(dgvMain.GetDgvTotalsSummary.DGV);
        }
        #endregion

        #region Showing Buttons
        #region Fields - Properties
        private bool _IsPrintable = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsPrintable { get => _IsPrintable; set => ShowPrintButtons(value); }
        public bool _IsImportable = true;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsImportable { get => _IsImportable; set => ShowImportButtons(value); }
        public bool _IsExportable = true;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsExportable { get => _IsExportable; set => ShowExportButtons(value); }
        private bool _IsListOnly = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsListOnly { get => _IsListOnly; set => SetIsListOnlyButtons(value); }
        #endregion

        #region Methods
        private void ShowPrintButtons(bool value)
        {
            _IsPrintable = value;
            tsbtnPrint.Visible = value;
            tssPrint.Visible = value;
        }
        private void ShowImportButtons(bool value)
        {
            _IsImportable = value;
            tsbtnImport.Visible = value;
            tssImport.Visible = value;
        }
        private void ShowExportButtons(bool value)
        {
            _IsExportable = value;
            tsbtnExport.Visible = value;
            tssExport.Visible = value;
        }
        private void SetIsListOnlyButtons(bool value)
        {
            _IsListOnly = value;
            tsbtnNew.Visible = !value;
            tssNew.Visible = !value;
            tsbtnEdit.Visible = !value;
            tssEdit.Visible = !value;
            tsbtnDelete.Visible = !value;
            tssDelete.Visible = !value;
            tsbtnImport.Visible = !value;
            tssImport.Visible = !value;
        }
        #endregion
        #endregion

        #region Filter Action Handlers
        #region Fields - Properties
        protected List<ControlsValueModel> listControlValue { get; set; } = new List<ControlsValueModel>();
        private LeadTeamsLabel lblSearchValues = new LeadTeamsLabel()
        {
            Name = "lblSearchValues",
            Anchor = AnchorStyles.Right | AnchorStyles.Left,
            TextAlign = ContentAlignment.MiddleLeft,
        };
        private LeadTeamsButton btnClearFilter = new LeadTeamsButton()
        {
            Name = "btnClearFilter",
            Anchor = AnchorStyles.Right | AnchorStyles.Left,
            Text = "Clear Filter",
        };
        private LeadTeamsTableLayoutPanel tlpFilter = new LeadTeamsTableLayoutPanel()
        {
            Dock = DockStyle.Fill,
            RowCount = 1,
            ColumnCount = 2,
        };
        private bool _isSearched = false;
        protected bool IsSearched
        {
            get => _isSearched;
            set
            {
                _isSearched = value;
                if (value)
                {
                    lblSearchValues.Text = SearchUtilities.GetListControlsValues(listControlValue);
                    if (tlpMain.RowCount == 1)
                    {
                        tlpMain.RowCount = 2;
                        tlpMain.RowStyles.Add(new RowStyle() { Height = 35, SizeType = SizeType.Absolute });
                        tlpMain.Controls.Add(tlpFilter, 0, 1);
                        tlpMain.Size = new Size(tlpMain.Size.Width, 55 + 35);
                    }
                }
                else
                {
                    if (tlpMain.RowCount == 2)
                    {
                        Clear();
                    }
                }
            }
        }
        protected DateTime TransactionDateFrom => ValidateValue.ValidateDateTime(SearchUtilities.GetSearchValue(listControlValue)).Date;
        protected DateTime TransactionDateTo => ValidateValue.ValidateDateTime(SearchUtilities.GetSearchValue(listControlValue)).Date;
        #endregion

        #region Methods
        public virtual void Clear()
        {
            Utilities.RemoveArbitraryRow(tlpMain, 1);
            listControlValue = new List<ControlsValueModel>();
            tlpMain.Size = new Size(tlpMain.Size.Width, 55);
            LoadData(PageNumber, PageSize);
        }
        #endregion
        #endregion

        #region Pagination
        protected int PageNumber => dgvMain.PageNumber;
        protected int PageSize => dgvMain.PageSize;

        private void PaginationList_LoadData(object? sender, EventArgs e)
        {
            LoadData(PageNumber, PageSize);
        }

        private void ApplyPaginationEvents(PaginationDataGridView paginationList)
        {
            paginationList.RefreshData += PaginationList_LoadData;
            paginationList.Previous += PaginationList_LoadData;
            paginationList.Next += PaginationList_LoadData;
            paginationList.First += PaginationList_LoadData;
            paginationList.Last += PaginationList_LoadData;
        }
        #endregion

        protected void LoadData(int pageNumber, int pageSize)
        {
            LoadingHelper.ShowLoading("Loading ... Please wait ........", o =>
            {
                this.SafelyInvokeAction(() =>
                {
                    PaginationList paginationList = LoadAllData(pageNumber, pageSize);
                    dgvMain.DataSource = paginationList;
                });
            });
        }

        public virtual PaginationList LoadAllData(int pageNumber, int pageSize)
        {
            throw new NotImplementedException("Need To Override The LoadAllData Method To Return The PaginationList Data");
        }

        protected void SetColumnDisplayIndex(string columnName, int displayIndex)
        {
            if (dgvMain.GetDgvTotalsSummary.DGV.Columns.Contains(columnName))
                dgvMain.GetDgvTotalsSummary.DGV.Columns[columnName].DisplayIndex = displayIndex;
        }

        protected void SetColumnVisable(string columnName, bool visible)
        {
            if (dgvMain.GetDgvTotalsSummary.DGV.Columns.Contains(columnName))
                dgvMain.GetDgvTotalsSummary.DGV.Columns[columnName].Visible = visible;
        }
    }
}
