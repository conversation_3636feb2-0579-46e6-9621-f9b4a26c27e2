﻿namespace LeadTeams.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthenticationController : ControllerBase
    {
        private readonly IAuthenticationService _authenticationService;

        public AuthenticationController(IAuthenticationService authenticationService)
        {
            _authenticationService = authenticationService;
        }

        [HttpPost("Login")]
        public async Task<IActionResult> Login([FromBody] Models.ModelDTO.Authentication.LoginRequest model)
        {
            try
            {
                var result = await _authenticationService.Login(model);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("RefreshTokenAsync")]
        [HttpGet("RefreshTokenAsync")]
        public async Task<IActionResult> RefreshTokenAsync([FromQuery] string? token, [FromBody] RefreshTokenRequest? refreshTokenRequest)
        {
            // Get the token from either the query parameter, the request body, or the cookie
            var refreshToken = token ?? refreshTokenRequest?.Token ?? Request.Cookies[AuthenticationDefaults.RefreshTokenCookie];

            if (string.IsNullOrEmpty(refreshToken))
                return BadRequest("Token is required!");

            try
            {
                // Create a new request object with the token
                var tokenRequest = new RefreshTokenRequest() { Token = refreshToken };
                var result = await _authenticationService.RefreshTokenAsync(tokenRequest);

                // Set the refresh token in a cookie for web clients
                if (Request.Headers.ContainsKey("User-Agent") &&
                    Request.Headers["User-Agent"].ToString().Contains("Mozilla"))
                {
                    Response.Cookies.Append(
                        AuthenticationDefaults.RefreshTokenCookie,
                        result.RefreshToken,
                        new CookieOptions
                        {
                            HttpOnly = true,
                            Secure = true,
                            SameSite = SameSiteMode.Strict,
                            Expires = result.RefreshTokenExpiration
                        });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("RevokeTokenAsync")]
        public async Task<IActionResult> RevokeTokenAsync(string revokeToken)
        {
            var token = revokeToken ?? Request.Cookies[AuthenticationDefaults.RefreshTokenCookie];

            if (string.IsNullOrEmpty(token))
                return BadRequest("Token is required!");

            try
            {
                var result = await _authenticationService.RevokeTokenAsync(token);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }
    }
}
