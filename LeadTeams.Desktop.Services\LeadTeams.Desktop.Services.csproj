<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <Version>1.6.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Desktop.Loading\GMCadiomCore.Desktop.Loading.csproj" />
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Desktop\GMCadiomCore.Desktop.csproj" />
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Repositories\GMCadiomCore.Repositories.csproj" />
    <ProjectReference Include="..\LeadTeams.Desktop.Controls\LeadTeams.Desktop.Controls.csproj" />
    <ProjectReference Include="..\LeadTeams.PermissionAndSession\LeadTeams.PermissionAndSession.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="BaseView\BaseEntryGenericView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="BaseView\BaseEntryView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="BaseView\BaseListGenericView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="BaseView\BaseListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="BaseView\BaseReportGenericView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="BaseView\BaseReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="BaseView\SearchBoxGenericView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="BaseView\SearchBox.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>

</Project>
