﻿namespace GMCadiomCore.Desktop.CustomControls.dgv
{
    public partial class dgvViewList<T> : UserControl, IPaginationList
    {
        private bool isInitialize = false;

        private BindingSource MainBindingSource;
        public BindingSource SmallBindingSource = new BindingSource();
        //Select Column Name To Sum
        public string[] NewSummaryColumns
        {
            set
            {
                //Show The SummaryBox
                dgv.SummaryColumns = value;
                if (value.Length > 0)
                {
                    dgv.CreateSummaryRow();
                }
            }
        }

        PaginationList<T>? list;

        private frmColumnsVisibility frmColumnsVisibility;

        private string path => SessionPaths.SessionDataGridViewConfigurationsPath();
        private string dataGridViewName => typeof(T).Name;
        private string configPath => Path.Combine(path, dataGridViewName);
        public List<ColumnVisibility> LoadedItems { get; private set; }

        public dgvViewList(BindingSource bs)
        {
            InitializeComponent();

            cmbPageSize.SelectedIndex = 1;

            MainBindingSource = bs;

            frmColumnsVisibility = new frmColumnsVisibility();

            Eventss();

            LoadedItems = JsonUtilities.ReadFromJson<List<ColumnVisibility>>(configPath) ?? new List<ColumnVisibility>();
        }

        // Handle CheckedComboBox item check/uncheck to show/hide DataGridView columns
        private void checkedComboBox_CheckedItemsChanged(ColumnVisibility columnVisibility)
        {
            if (columnVisibility == null)
                return;

            var column = dgv.DGV.Columns.OfType<DataGridViewColumn>().Where(x => x.Name.Equals(columnVisibility.ColumnName)).ToList();
            if (column.Count() > 0)
            {
                dgv.DGV.Columns[column[0].Name].Visible = columnVisibility.IsVisible;
            }
            JsonUtilities.SaveToJson(frmColumnsVisibility.Items, configPath);
        }

        private void Eventss()
        {
            this.Load += (s, e) =>
            {
                isInitialize = true;
                FillDGV();
            };

            btnColumnsVisibility.Click += (s, e) => frmColumnsVisibility.ShowDialog();

            // Attach event handler to handle check/uncheck events
            frmColumnsVisibility.CheckedItemsChanged += checkedComboBox_CheckedItemsChanged;

            cmbPageSize.SelectedIndexChanged += (s, e) => RefreshData?.Invoke(s, e);

            MainBindingSource.DataSourceChanged += (s, e) => FillDGV();

            btnPrevious.Click += (s, e) => Previous?.Invoke(s, e);

            btnNext.Click += (s, e) => Next?.Invoke(s, e);

            btnFirst.Click += (s, e) => First?.Invoke(s, e);

            btnLast.Click += (s, e) => Last?.Invoke(s, e);
        }

        public void FillDGV()
        {
            this.SafelyInvokeAction(() =>
            {
                list = MainBindingSource.DataSource as PaginationList<T>;
                if (list != null)
                {
                    btnPrevious.Enabled = list.HasPreviousPage;
                    btnNext.Enabled = list.HasNextPage;
                    SmallBindingSource.DataSource = list.Items;
                    dgv.DataSource = SmallBindingSource;
                    if (dgv.SummaryColumns?.Length > 0)
                        dgv.CreateSummaryRow();

                    if (isInitialize)
                    {
                        if (LoadedItems != null)
                            LoadedItems.ForEach(columnVisibility => frmColumnsVisibility.AddItem(columnVisibility));

                        foreach (DataGridViewColumn column in dgv.DGV.Columns)
                        {
                            ColumnVisibility columnVisibility = new ColumnVisibility()
                            {
                                ColumnHeaderText = column.HeaderText,
                                ColumnName = column.Name,
                                IsVisible = column.Visible
                            };
                            frmColumnsVisibility.AddItem(columnVisibility);
                            if (column.GetType() == typeof(DataGridViewImageColumn))
                                ((DataGridViewImageColumn)column).ImageLayout = DataGridViewImageCellLayout.Zoom;
                        }
                    }
                }
            });
        }

        public void RefreshList() => FillDGV();
        public dgvTotalsSummary GetdgvTotalsSummary => dgv;

        // Properties
        public string Header { set { this.SafelyInvokeAction(() => lblHeader.Text = value); } }
        public int PageSize
        {
            get
            {
                int pageSize = 0;
                this.SafelyInvokeAction(() =>
                {
                    pageSize = ValidateValue.ValidateInt(cmbPageSize.SelectedItem);
                    if (pageSize == 0)
                        pageSize = 25;
                });
                return pageSize;
            }
        }

        // Events
        public event EventHandler RefreshData;
        public event EventHandler Previous;
        public event EventHandler Next;
        public event EventHandler First;
        public event EventHandler Last;
    }
}
