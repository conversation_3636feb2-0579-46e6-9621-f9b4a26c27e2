﻿namespace LeadTeams.Client.DI
{
    public static class GetInjectedServices
    {
        public static TService GetServiceByType<TService>() where TService : class
        {
            TService? service = null;
            service = ServiceProviderHolder.ServiceProvider.GetService<TService>();
            return service ?? throw new NotImplementedException();
        }

        public static TForm GetFormByType<TForm>() where TForm : LeadTeamsForm
        {
            TForm? form = null;
            form = ServiceProviderHolder.ServiceProvider.GetService<TForm>();
            return form ?? throw new NotImplementedException();
        }

        public static Form? GetFormByName(string OpenFormName)
        {
            Form? form = null;
            switch (OpenFormName)
            {
                #region AskLeaveList
                case (nameof(AskLeaveListView)): form = ServiceProviderHolder.ServiceProvider.GetService<AskLeaveListView>(); break;
                case (nameof(AskLeaveView)): form = ServiceProviderHolder.ServiceProvider.GetService<AskLeaveView>(); break;
                    #endregion
            }
            return form;
        }

        public static void OpenFormByName(string OpenFormName, Form? parentForm = null)
        {
            Form? form = GetFormByName(OpenFormName);
            if (form != null)
            {
                if (parentForm != null)
                {
                    form.MdiParent = parentForm;
                    form.Show();
                }
                else
                    form.ShowDialog();
            }
        }

        public static void OpenFormByModel(object model)
        {
            switch (model)
            {
                case AskLeaveViewModel askLeaveModel:
                    {
                        IAskLeaveService service = GetServiceByType<IAskLeaveService>();
                        ISession session = GetServiceByType<ISession>();
                        AskLeaveView view = new AskLeaveView(session, service);
                        view.ShowDialog();
                        break;
                    }
                default: MessageBox.Show(model.ToString()); break;
            }
        }
    }
}
